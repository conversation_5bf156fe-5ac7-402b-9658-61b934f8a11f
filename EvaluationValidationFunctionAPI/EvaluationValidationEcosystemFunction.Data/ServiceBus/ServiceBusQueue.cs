﻿using AutoMapper;
using Azure.Messaging.ServiceBus;
using EvaluationValidationEcosystemFunction.Domain.ServiceBusMessage;
using Microsoft.Extensions.Azure;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EvaluationValidationEcosystemFunction.Data.ServiceBus
{

    public class ServiceBusQueue<T> :  IServiceBusQueue<T>
    {

        private readonly ServiceBusClient _client;
        private readonly ServiceBusSender _sender;

        private readonly IMapper _mapper;


        public ServiceBusQueue(IAzureClientFactory<ServiceBusClient> clientFactory, IMapper mapper)
        {
            _client = clientFactory.CreateClient("ServiceBusQueue");            
            _sender = _client.CreateSender(Environment.GetEnvironmentVariable("EventQueue"));
            //_sender = _client.CreateSender("queue-nrcan-oee-bulk-evaluation-processing-dev");
            _mapper = mapper;
        }

        public async Task Send(List<ServiceBusEvent<T>> data)
        {
            using (ServiceBusMessageBatch messageBatch = await _sender.CreateMessageBatchAsync())
            {
                var datetime = System.DateTime.UtcNow;                
                foreach (ServiceBusEvent<T> item in data)
                {
                    if (!messageBatch.TryAddMessage(new ServiceBusMessage(JsonConvert.SerializeObject(item))
                    {                        
                        ScheduledEnqueueTime = datetime,
                    }))
                    {
                        // if it is too large for the batch
                        throw new Exception($"The message is too large to fit in the batch.");
                    }                    

                }
                
                await _sender.SendMessagesAsync(messageBatch);
            }
        }

    }
}
