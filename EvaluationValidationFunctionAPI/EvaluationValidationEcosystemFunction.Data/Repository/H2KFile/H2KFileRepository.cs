﻿using EvaluationValidationEcosystemFunction.Domain.Request;
using EvaluationValidationEcosystemFunction.Domain.Rule;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using System.IO;
using System;

namespace EvaluationValidationEcosystemFunction.Data.Repository.H2KFile
{
    public class H2KFileRepository : ADLDbContext, IH2KFileRepository
    {
        private readonly ADLDbContext _context;
        public H2KFileRepository(ADLDbContext context)           
        {
            this._context = context;
        }
        public async Task<string> GetH2KFileAsync(EvaluationFile h2k)
        {                       
            // Create client connection
            BlobServiceClient client = new BlobServiceClient(_context.SAS);
            
            // Create a container reference
            BlobContainerClient container = client.GetBlobContainerClient(h2k.ADLLandingContainerName);            

            //string blobPath = $"Landing/{}";
            // Create a blob reference
            
            BlobClient blob = container.GetBlobClient($"{h2k.ADLLandingDirectoryName}{h2k.EvaluationFileName}");
            try
            {
                if (await blob.ExistsAsync())
                {

                    Stream stream = blob.OpenRead();
                    StreamReader reader = new StreamReader(stream);
                    string h2kFile = reader.ReadToEnd();

                    return h2kFile;
                }
                else
                {
                    return null;
                }
            }
            catch(Exception e)
            {
                Console.WriteLine(e.Message);
                return null;
            }
        }

        public async Task<string> GetH2KFileBatchAsync(EvaluationFileBatch h2k)
        {
            // Create client connection
            BlobServiceClient client = new BlobServiceClient(_context.SAS);

            // Create a container reference
            BlobContainerClient container = client.GetBlobContainerClient(h2k.ADLContainerName);

            //string blobPath = $"Landing/{}";
            // Create a blob reference

            BlobClient blob = container.GetBlobClient($"{h2k.ADLDirectoryName}{h2k.EvaluationFileName}");

            if (await blob.ExistsAsync())
            {

                Stream stream = blob.OpenRead();
                StreamReader reader = new StreamReader(stream);
                string h2kFile = reader.ReadToEnd();

                return h2kFile;
            }
            else
            {
                return null;
            }
        }
    }
}