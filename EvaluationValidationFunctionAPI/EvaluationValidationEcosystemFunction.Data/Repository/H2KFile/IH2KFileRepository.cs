﻿using EvaluationValidationEcosystemFunction.Domain.Request;
using EvaluationValidationEcosystemFunction.Domain.Rule;
using System.Threading.Tasks;
using System.IO;


namespace EvaluationValidationEcosystemFunction.Data.Repository.H2KFile
{
    public interface IH2KFileRepository 
    {

        Task<string> GetH2KFileAsync(EvaluationFile h2k);
        Task<string> GetH2KFileBatchAsync(EvaluationFileBatch h2k);


    }
}