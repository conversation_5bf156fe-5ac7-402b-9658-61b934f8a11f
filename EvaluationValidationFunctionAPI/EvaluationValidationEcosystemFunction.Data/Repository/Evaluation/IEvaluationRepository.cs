﻿using EvaluationValidationEcosystemFunction.Domain.Rule;
using EvaluationValidationEcosystemFunction.Domain.Evaluation;
using System.Data;
using System.Collections.Generic;
using EvaluationValidationEcosystemFunction.Domain.Response;

namespace EvaluationValidationEcosystemFunction.Data.Repository.Evaluation
{
    public interface IEvaluationRepository
    {

        ServiceResponse<List<EvalutionStatusResponse>> SetEvaluationStatus(DataTable evaluations, string evaluationStatus);
        
    }
}