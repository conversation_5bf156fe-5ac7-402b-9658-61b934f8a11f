﻿using EvaluationValidationEcosystemFunction.Data.Common;
using EvaluationValidationEcosystemFunction.Domain.Evaluation;
using EvaluationValidationEcosystemFunction.Domain.Response;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Data;

namespace EvaluationValidationEcosystemFunction.Data.Repository.Evaluation
{
    public class EvaluationRepository : DbContext, IEvaluationRepository
    {
        private readonly DbContext _context;
        public EvaluationRepository(DbContext context)           
        {
            this._context = context;
        }
       
      
        public ServiceResponse<List<EvalutionStatusResponse>> SetEvaluationStatus(DataTable evaluations, string evaluationStatus)
        {
            var setEval = "EEAPI.s_SetEvaluationStatus";
            DbContext context = new DbContext();
            SqlParameter paramEval = new SqlParameter("@Evaluations", SqlDbType.Structured);
            paramEval.Value = evaluations;
            SqlParameter paramSubType = new SqlParameter("@EvaluationStatus", SqlDbType.NVarChar);
            paramSubType.Value = evaluationStatus;

            SqlParameter paramJSONOutput = new SqlParameter("@jsonOutput", SqlDbType.NVarChar, -1);
            paramJSONOutput.Direction = ParameterDirection.Output;

            string json = SQLHelper.ExecuteNonQueryJSON(_context.connectionString, setEval, System.Data.CommandType.StoredProcedure, "@jsonOutput", paramEval, paramSubType, paramJSONOutput);
            var result = JsonConvert.DeserializeObject<List<EvalutionStatusResponse>>(json);
            return new ServiceResponse<List<EvalutionStatusResponse>>() { Data = result, Success = true };

        }
    }
}