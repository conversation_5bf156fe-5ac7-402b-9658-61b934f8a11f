﻿using EvaluationValidationEcosystemFunction.Data.Common;
using EvaluationValidationEcosystemFunction.Data.Helper;
using EvaluationValidationEcosystemFunction.Domain.ManualEvaluation;
using EvaluationValidationEcosystemFunction.Domain.Response;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using Microsoft.Data.SqlClient;

namespace EvaluationValidationEcosystemFunction.Data.Repository.ManualEvaluation
{
    public class ManualEvaluationRepository : DbContext, IManualEvaluationRepository
    {
        private readonly DbContext _context;
        public ManualEvaluationRepository(DbContext context)
        {
            this._context = context;
        }
        public int SetManualEvaluationRequest(EvaluationRequest request)
        {          
            var logCommandText = "[EASO].[s_SetManualEvaluationRequest]";
            SqlParameter paramLogs = new SqlParameter("@request", SqlDbType.Structured);
            paramLogs.Value = request.ToDataTable();

            SqlParameter supportingDocs = new SqlParameter("@suportdocs", SqlDbType.Structured);
            supportingDocs.Value = request.ManualEvaluationSuportDocs.ToDataTable();

            var output = Convert.ToInt32(SQLHelper.ExecuteScalar(_context.connectionString, logCommandText, System.Data.CommandType.StoredProcedure, paramLogs, supportingDocs));
            
            return output;
        }
        public bool SetManualEvaluationRequestAssignTo(long id,string user)
        {
            var logCommandText = "[EASO].[s_SetManualEvaluationRequestAssignTo]";
            SqlParameter paramId = new SqlParameter("@Id", SqlDbType.BigInt);
            paramId.Value = id;
            SqlParameter paramUser = new SqlParameter("@User", SqlDbType.NVarChar);
            paramUser.Value = user;
            return SQLHelper.ExecuteNonQuery(_context.connectionString, logCommandText, System.Data.CommandType.StoredProcedure, paramId, paramUser) > 0;
        }
        public ServiceResponse<ManualEvaluationSearchResult> SearchManualEvaluationById(long id)
        {
            var logCommandText = "[EASO].[s_GetManualEvaluationRequestByIdJSON]";
            SqlParameter paramId = new SqlParameter("@Id", SqlDbType.BigInt);
            paramId.Value = id;            
            SqlParameter paramJSONOutput = new SqlParameter("@Output", SqlDbType.NVarChar, -1);
            paramJSONOutput.Direction = ParameterDirection.Output;
            string json = SQLHelper.ExecuteNonQueryJSON(_context.connectionString, logCommandText, System.Data.CommandType.StoredProcedure, "@Output", paramId, paramJSONOutput);
            var result = JsonConvert.DeserializeObject<List<ManualEvaluationSearchResult>>(json);
            return new ServiceResponse<ManualEvaluationSearchResult>(){Data= result[0], Success = true };
        }
        public PagedResponse<List<ManualEvaluationSearchResult>> SearchManualEvaluationRequests(ManualEvaluationSearchParameter searchParameter)
        {
                var rulesCommandTextJSON = "[EASO].[s_GetManualEvaluationRequestJSON]";
            SqlParameter paramProvince = new SqlParameter("@Province", SqlDbType.NVarChar);

            if (searchParameter.Province == null)
                paramProvince.Value = DBNull.Value;
            else
                paramProvince.Value = searchParameter.Province;

            SqlParameter paramServiceOrganization = new SqlParameter("@ServiceOrganization", SqlDbType.NVarChar);

            if (searchParameter.Province == null)
                paramServiceOrganization.Value = DBNull.Value;
            else
                paramServiceOrganization.Value = searchParameter.ServiceOrganization;

            SqlParameter paramStatus = new SqlParameter("@Status", SqlDbType.NVarChar);
            if (searchParameter.Status == null)
                paramStatus.Value = DBNull.Value;
            else
                paramStatus.Value = searchParameter.Status;

            SqlParameter paramTypeOfRequest = new SqlParameter("@TypeOfRequest", SqlDbType.NVarChar);
            if (searchParameter.TypeOfRequest == null)
                paramTypeOfRequest.Value = DBNull.Value;
            else
                paramTypeOfRequest.Value = searchParameter.TypeOfRequest;

            SqlParameter paramStartDate = new SqlParameter("@StartDate", SqlDbType.DateTime);
            if (DateTime.MinValue.Date.Equals(searchParameter.StartDate.Date))
                paramStartDate.Value = DBNull.Value;
            else
                paramStartDate.Value =searchParameter.StartDate.Date;// searchParameter.StartDate;


            SqlParameter paramEndDate = new SqlParameter("@EndDate", SqlDbType.DateTime);
            if (DateTime.MinValue.Date.Equals(searchParameter.EndDate.Date))
                paramEndDate.Value = DBNull.Value;
            else
                paramEndDate.Value = searchParameter.EndDate.Date;

            SqlParameter paramOrderBy = new SqlParameter("@OrderBy", SqlDbType.NVarChar);
            paramOrderBy.Value = searchParameter.OrderBy;

            SqlParameter paramOrderByField = new SqlParameter("@OrderByField", SqlDbType.NVarChar);
            paramOrderByField.Value = searchParameter.OrderByField;

            SqlParameter paramOffset = new SqlParameter("@Offset", SqlDbType.Int);
            paramOffset.Value = searchParameter.Offset;

            SqlParameter paramLimit = new SqlParameter("@Limit", SqlDbType.Int);
            paramLimit.Value = searchParameter.Limit;
                        
            SqlParameter paramJSONOutput = new SqlParameter("@Output", SqlDbType.NVarChar, -1);
            paramJSONOutput.Direction = ParameterDirection.Output;

            SqlParameter paramTotalOuput = new SqlParameter("@TotalRecord", SqlDbType.Int);
            paramTotalOuput.Direction = ParameterDirection.Output;


            string json = SQLHelper.ExecuteNonQueryJSON(_context.connectionString, rulesCommandTextJSON, System.Data.CommandType.StoredProcedure, "@Output", paramProvince,
                paramServiceOrganization, paramStatus, paramTypeOfRequest, paramStartDate, paramEndDate, paramOrderBy, paramOrderByField, paramOffset, paramLimit,paramJSONOutput, paramTotalOuput);

            var result = JsonConvert.DeserializeObject<List<ManualEvaluationSearchResult>>(json);

            return new PagedResponse<List<ManualEvaluationSearchResult>>(result, searchParameter.Offset, searchParameter.Limit, (Int32)paramTotalOuput.Value) { Success=true}  ;
        }
    }
}