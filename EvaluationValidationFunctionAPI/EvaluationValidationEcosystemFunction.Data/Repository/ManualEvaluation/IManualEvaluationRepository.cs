﻿using EvaluationValidationEcosystemFunction.Domain.ManualEvaluation;
using EvaluationValidationEcosystemFunction.Domain.Response;
using System.Collections.Generic;

namespace EvaluationValidationEcosystemFunction.Data.Repository.ManualEvaluation
{
    public interface IManualEvaluationRepository
    {
        int SetManualEvaluationRequest(EvaluationRequest runLogs);
        ServiceResponse<ManualEvaluationSearchResult> SearchManualEvaluationById(long id);
        PagedResponse<List<ManualEvaluationSearchResult>> SearchManualEvaluationRequests(ManualEvaluationSearchParameter searchParameter);
        bool SetManualEvaluationRequestAssignTo(long id, string user);


    }
}