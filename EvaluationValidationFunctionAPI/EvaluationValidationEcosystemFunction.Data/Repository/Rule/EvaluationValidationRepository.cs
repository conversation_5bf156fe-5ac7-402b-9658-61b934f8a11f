﻿using EvaluationValidationEcosystemFunction.Domain.Request;
using EvaluationValidationEcosystemFunction.Domain.Response;
using EvaluationValidationEcosystemFunction.Domain.Rule;
using EvaluationValidationEcosystemFunction.Domain.Evaluation;
using EvaluationValidationEcosystemFunction.Data.Common;
using EvaluationValidationEcosystemFunction.Data.Repository;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json;
using System;

namespace EvaluationValidationEcosystemFunction.Data.Repository.Rule
{
    public class EvaluationValidationRepository : DbContext, IEvaluationValidationRepository
    {
        private readonly DbContext _context;
        public EvaluationValidationRepository(DbContext context)           
        {
            this._context = context;
        }
        public EvaluationType GetRules(EvaluationType eval, string language)
        {
            var rulesCommandTextJSON = "Rules.s_GetRulesForFileInformationJSON";
            SqlParameter paramDate = new SqlParameter("@Date", SqlDbType.DateTime2);
            paramDate.Value = System.DateTime.UtcNow;
            SqlParameter paramProgram = new SqlParameter("@Program", SqlDbType.NVarChar);
            paramProgram.Value = eval.Program;
            SqlParameter paramFileType = new SqlParameter("@FileType", SqlDbType.NVarChar);
            paramFileType.Value = eval.FileType;
            SqlParameter paramVersion = new SqlParameter("@FileVersion", SqlDbType.NVarChar);
            paramVersion.Value = eval.FileVersion;
            SqlParameter paramLanguage = new SqlParameter("@Language", SqlDbType.NVarChar);
            paramLanguage.Value = language;
            SqlParameter paramSubmissionType = new SqlParameter("@SubmissionType", SqlDbType.NVarChar);
            paramSubmissionType.Value = eval.SubmissionType;


            SqlParameter paramJSONOutput = new SqlParameter("@jsonOutput", SqlDbType.NVarChar, -1);
            paramJSONOutput.Direction = ParameterDirection.Output;
            
            string json = SQLHelper.ExecuteNonQueryJSON(_context.connectionString, rulesCommandTextJSON, System.Data.CommandType.StoredProcedure, "@jsonOutput", paramDate,
                paramProgram, paramFileType, paramVersion, paramLanguage,paramSubmissionType, paramJSONOutput);

            return JsonConvert.DeserializeObject<EvaluationType>(json);
        }
        public string GetLookUpRuleResults(int ruleDefinitionId, string whereFilter, string fileNumber, string previousFileId)
        {
            var lookupValueCommandText = "Rules.s_GetRuleLookUpValue";
            DbContext context = new DbContext();
            SqlParameter paramWhereFilter = new SqlParameter("@WhereFilter", SqlDbType.NVarChar);
            paramWhereFilter.Value = whereFilter;
            SqlParameter paramRuleDefinitionIdluv = new SqlParameter("@RuleDefinitionId", SqlDbType.Int);
            paramRuleDefinitionIdluv.Value = ruleDefinitionId;
            SqlParameter paramCurrentFileNumber = new SqlParameter("@CurrentFileNumber", SqlDbType.NVarChar);
            paramCurrentFileNumber.Value = fileNumber;

            SqlParameter paramDate = new SqlParameter("@Date", SqlDbType.DateTime2);
            paramDate.Value = System.DateTime.UtcNow;

            var item = SQLHelper.ExecuteScalar(_context.connectionString,
                lookupValueCommandText, System.Data.CommandType.StoredProcedure, paramRuleDefinitionIdluv, paramWhereFilter, paramDate, paramCurrentFileNumber);
            
            return item == null || item == System.DBNull.Value ? null : item.ToString();
        }

        public EvaluationFileBatch GetEvaluationFileBatch(string FileNumber)
        {
            var lookupValueCommandText = "Rules.s_GetEvaluationFileBatchInfoJSON";
            DbContext context = new DbContext();
            SqlParameter paramFileNumber = new SqlParameter("@FileNumber", SqlDbType.NVarChar);
            paramFileNumber.Value = FileNumber;
            SqlParameter paramJSONOutput = new SqlParameter("@jsonOutput", SqlDbType.NVarChar, -1);
            paramJSONOutput.Direction = ParameterDirection.Output;

            return JsonConvert.DeserializeObject<EvaluationFileBatch>(SQLHelper.ExecuteNonQueryJSON(_context.connectionString,
                lookupValueCommandText, System.Data.CommandType.StoredProcedure, "@jsonOutput", paramFileNumber, paramJSONOutput));
        }
        public int SetLookUpRuleResults(DataTable runLogs, int evaluationsId)
        {
            var logCommandText = "Rules.s_SetRunLog";
            DbContext context = new DbContext();
            SqlParameter paramLogs = new SqlParameter("@RunLogs", SqlDbType.Structured);
            paramLogs.Value = runLogs;
            return (int)SQLHelper.ExecuteScalar(_context.connectionString, logCommandText, System.Data.CommandType.StoredProcedure, paramLogs);
        }
        public int SetEvaluation(DataTable evaluation, string submissionType)
        {
            var setEval = "Rules.s_SetEvaluations";
            DbContext context = new DbContext();
            SqlParameter paramEval = new SqlParameter("@Evaluations", SqlDbType.Structured);
            paramEval.Value = evaluation;
            SqlParameter paramSubType = new SqlParameter("@SubmissionType", SqlDbType.NVarChar);
            paramSubType.Value = submissionType;

            return (int)SQLHelper.ExecuteScalar(_context.connectionString, setEval, System.Data.CommandType.StoredProcedure, paramEval, paramSubType);
             
        }
        public int SetERSExtention(DataTable ers, string submissionType)
        {
            var setEval = "Rules.s_SetERSExtentions";
            DbContext context = new DbContext();
            SqlParameter paramEval = new SqlParameter("@ERS", SqlDbType.Structured);
            paramEval.Value = ers;
            SqlParameter paramSubType = new SqlParameter("@SubmissionType", SqlDbType.NVarChar);
            paramSubType.Value = submissionType;

            return (int)SQLHelper.ExecuteNonQuery(_context.connectionString, setEval, System.Data.CommandType.StoredProcedure, paramEval, paramSubType);

        }

        public List<UpdateEvaluationDifference> GetEvaluationDifference(DataTable evaluation)
        {
            var setEvalDifference = "Rules.s_GetEvaluationDifferenceJSON";
            DbContext context = new DbContext();
            SqlParameter paramEval = new SqlParameter("@Evaluations", SqlDbType.Structured);
            paramEval.Value = evaluation;
            SqlParameter paramJSONOutput = new SqlParameter("@JSONOutput", SqlDbType.NVarChar, -1);
            paramJSONOutput.Direction = ParameterDirection.Output;

            string json = SQLHelper.ExecuteNonQueryJSON(_context.connectionString, setEvalDifference, System.Data.CommandType.StoredProcedure, "@JSONOutput", paramJSONOutput, paramEval);
            
            return JsonConvert.DeserializeObject<List<UpdateEvaluationDifference>>(json);

        }

    }
}