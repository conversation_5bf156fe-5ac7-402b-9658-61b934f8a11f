﻿using EvaluationValidationEcosystemFunction.Domain.Rule;
using EvaluationValidationEcosystemFunction.Domain.Evaluation;
using System.Data;
using System.Collections.Generic;

namespace EvaluationValidationEcosystemFunction.Data.Repository.Rule
{
    public interface IEvaluationValidationRepository
    {
        EvaluationType GetRules(EvaluationType eval, string language);
        string GetLookUpRuleResults(int ruleDefinitionId, string whereFilter, string fileNumber, string previousFileId);
        int SetLookUpRuleResults(DataTable runLogs, int evaluationsId);
        int SetEvaluation(DataTable evaluation, string SubmissionType);
        EvaluationFileBatch GetEvaluationFileBatch(string FileNumber);
        List<UpdateEvaluationDifference> GetEvaluationDifference(DataTable evaluation);
        int SetERSExtention(DataTable ers, string submissionType);
    }
}