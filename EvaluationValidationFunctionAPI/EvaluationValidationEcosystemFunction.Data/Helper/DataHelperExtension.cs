﻿using EvaluationValidationEcosystemFunction.Domain;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;


namespace EvaluationValidationEcosystemFunction.Data.Helper
{
    public static class DataHelper
    {
        public static DataTable ToDataTable<T>(this T self)
        {

            var properties = typeof(T).GetProperties();

            var dataTable = new DataTable();
            foreach (var info in properties)
            {
                if (info.GetCustomAttributes(typeof(IgnoreToDataTableAttribute),false).Length==0)
                    dataTable.Columns.Add(info.Name, Nullable.GetUnderlyingType(info.PropertyType)?? info.PropertyType);
            }

            dataTable.Rows.Add(properties.Where(o => o.GetCustomAttributes(typeof(IgnoreToDataTableAttribute), false).Length == 0).Select(p => p.GetValue(self)).ToArray());

            return dataTable;
        }
        public static DataTable ToDataTable<T>(this IEnumerable<T> self)
        {
            if (self == null)
                return null;

            var properties = typeof(T).GetProperties();

            var dataTable = new DataTable();
            foreach (var info in properties)
            {
                if (info.GetCustomAttributes(typeof(IgnoreToDataTableAttribute), false).Length == 0)
                    dataTable.Columns.Add(info.Name, Nullable.GetUnderlyingType(info.PropertyType) ?? info.PropertyType);
            }

            foreach (var item in self)
                dataTable.Rows.Add(properties.Where(o => o.GetCustomAttributes(typeof(IgnoreToDataTableAttribute), false).Length == 0).Select(p => p.GetValue(item)).ToArray());

            return dataTable;
        }
    }
}
