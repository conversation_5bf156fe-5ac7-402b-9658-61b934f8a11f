﻿using System;
using System.Data;
using Microsoft.Data.SqlClient;
using System.Threading.Tasks;

namespace EvaluationValidationEcosystemFunction.Data.Common
{
    public class SQLHelper
    {



        public static Int32 ExecuteNonQuery(String connectionString, String commandText,
            CommandType commandType, params SqlParameter[] parameters)
        {
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand cmd = new SqlCommand(commandText, conn))
                {
                    // There're three command types: StoredProcedure, Text, TableDirect. The TableDirect   
                    // type is only for OLE DB.    
                    cmd.CommandType = commandType;
                    cmd.Parameters.AddRange(parameters);
                    cmd.CommandTimeout = conn.CommandTimeout;
                    conn.Open();
                    return cmd.ExecuteNonQuery();
                }
            }
        }
        public async static Task<int> ExecuteNonQueryAsync(String connectionString, String commandText,
            CommandType commandType, params SqlParameter[] parameters)
        {
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand cmd = new SqlCommand(commandText, conn))
                {
                    // There're three command types: StoredProcedure, Text, TableDirect. The TableDirect   
                    // type is only for OLE DB.    
                    cmd.CommandType = commandType;
                    cmd.Parameters.AddRange(parameters);

                    conn.Open();
                    return await cmd.ExecuteNonQueryAsync().ConfigureAwait(false);
                }
            }
        }
        public static string ExecuteNonQueryJSON(String connectionString, String commandText,
            CommandType commandType, string jsonOutputParam, params SqlParameter[] parameters)
        {

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                
                conn.Open();

                using (SqlCommand cmd = new SqlCommand(commandText, conn))
                {
                    cmd.CommandType = commandType;

                    // Create output parameter. "-1" is used for nvarchar(max)
                    cmd.Parameters.AddRange(parameters);
                    cmd.CommandTimeout = conn.ConnectionTimeout;
                    // Execute the command
                    cmd.ExecuteNonQuery();

                    // Get the values
                    return cmd.Parameters[jsonOutputParam].Value.ToString();

                }
            }
        }
        // Set the connection, command, and then execute the command and only return one value.  
        public static Object ExecuteScalar(String connectionString, String commandText,
            CommandType commandType, params SqlParameter[] parameters)
        {
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand cmd = new SqlCommand(commandText, conn))
                {
                    cmd.CommandType = commandType;
                    cmd.Parameters.AddRange(parameters);
                    cmd.CommandTimeout = conn.ConnectionTimeout;
                    conn.Open();
                    return cmd.ExecuteScalar();
                }
            }
        }

        // Set the connection, command, and then execute the command with query and return the reader.  
        public static SqlDataReader ExecuteReader(String connectionString, String commandText,
            CommandType commandType, params SqlParameter[] parameters)
        {
            SqlConnection conn = new SqlConnection(connectionString);

            using (SqlCommand cmd = new SqlCommand(commandText, conn))
            {
                cmd.CommandType = commandType;
                cmd.Parameters.AddRange(parameters);

                conn.Open();
                // When using CommandBehavior.CloseConnection, the connection will be closed when the   
                // IDataReader is closed.  
                SqlDataReader reader = cmd.ExecuteReader(CommandBehavior.CloseConnection);
                //cmd.Dispose();
                //conn.Close();
                return reader;
            }
        }
    }
}
