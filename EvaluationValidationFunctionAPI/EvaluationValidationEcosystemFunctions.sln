﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.31729.503
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EvaluationValidationEcosystemFunction", "EvaluationValidationEcosystemFunctions\EvaluationValidationEcosystemFunction.csproj", "{E5F6ED70-033B-440E-B465-C02776EB8D5B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EvaluationValidationEcosystemFunction.Data", "EvaluationValidationEcosystemFunction.Data\EvaluationValidationEcosystemFunction.Data.csproj", "{ED4DC2EB-5C5B-4A44-8063-9113CDA7BB30}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EvaluationValidationEcosystemFunction.Tasks", "EvaluationValidationEcosystemFunction.Tasks\EvaluationValidationEcosystemFunction.Tasks.csproj", "{DF4DAE82-16DB-4AF3-A780-8002CB128B2F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EvaluationValidationEcosystemFunction.Domain", "EvaluationValidationEcosystemFunction.Domain\EvaluationValidationEcosystemFunction.Domain.csproj", "{6276B5F1-428B-4FAF-AAD1-2B23F19706AA}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{2B26892F-F5B3-41D8-B37C-B408A8F1015C}"
	ProjectSection(SolutionItems) = preProject
		.pipelines\azure-pipelines.yml = .pipelines\azure-pipelines.yml
		.templates\environment-stage.yml = .templates\environment-stage.yml
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E5F6ED70-033B-440E-B465-C02776EB8D5B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E5F6ED70-033B-440E-B465-C02776EB8D5B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E5F6ED70-033B-440E-B465-C02776EB8D5B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E5F6ED70-033B-440E-B465-C02776EB8D5B}.Release|Any CPU.Build.0 = Release|Any CPU
		{ED4DC2EB-5C5B-4A44-8063-9113CDA7BB30}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ED4DC2EB-5C5B-4A44-8063-9113CDA7BB30}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ED4DC2EB-5C5B-4A44-8063-9113CDA7BB30}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ED4DC2EB-5C5B-4A44-8063-9113CDA7BB30}.Release|Any CPU.Build.0 = Release|Any CPU
		{DF4DAE82-16DB-4AF3-A780-8002CB128B2F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DF4DAE82-16DB-4AF3-A780-8002CB128B2F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DF4DAE82-16DB-4AF3-A780-8002CB128B2F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DF4DAE82-16DB-4AF3-A780-8002CB128B2F}.Release|Any CPU.Build.0 = Release|Any CPU
		{6276B5F1-428B-4FAF-AAD1-2B23F19706AA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6276B5F1-428B-4FAF-AAD1-2B23F19706AA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6276B5F1-428B-4FAF-AAD1-2B23F19706AA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6276B5F1-428B-4FAF-AAD1-2B23F19706AA}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {3BCF2455-CB1F-4C89-AC48-6192786C0B09}
	EndGlobalSection
EndGlobal
