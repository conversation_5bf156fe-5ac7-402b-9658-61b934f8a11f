# template for deploying to different verticals
#
parameters:
- name: stageName
- name: environmentName
- name: variableGroup
  default: 'EvaluationValidationFunctionAPI-dev'
- name: stageCondition
  type: object
  default: 'succeeded()'
- name: stageDependsOn
  type: object
  default: []


stages:
- stage: ${{ parameters.stageName }}
  condition: ${{ parameters.stageCondition }} 
  dependsOn: ${{ parameters.stageDependsOn }}
  jobs:
  - deployment: AppService
    displayName: 'Deploy to Azure Function'
    pool:
      vmImage: windows-latest
    variables:
      - group: ${{ parameters.variableGroup }}
    environment: ${{ parameters.environmentName }}
    strategy:
      runOnce:
        deploy:
          steps:
          - download: none
          - script: echo 'Deploying EvaluationValidationFunctionAPI to $(environment)'
          - task: DownloadPipelineArtifact@2
            displayName: 'Download API Web Package'
            inputs:
              buildType: 'current'
              artifactName: 'EvaluationValidationFunctionAPI'
              targetPath: '$(Pipeline.workspace)/package'
          - task: AzureFunctionApp@1
            displayName: 'Azure function Deploy: $(AppName)'
            inputs:
              azureSubscription: $(AzureSubscription)
              appName: $(AppName)
              package: '$(Pipeline.workspace)/package/a.zip'