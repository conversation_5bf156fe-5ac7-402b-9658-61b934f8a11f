﻿using EvaluationValidationEcosystemFunction.Domain.Evaluation;
using EvaluationValidationEcosystemFunction.Domain.Rule;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Reflection;
using System.Text;
using System.Xml;


namespace EvaluationValidationEcosystemFunction.Tasks.Helper
{
    public class DataTableHelper
    {
        public static DataTable CreateEvaluationDataTable(Evaluation evaluation , XmlDocument h2k, List<EvaluationXMLPathLookUp> lookups, out Evaluation eval)
        {
            eval = new Evaluation();
            eval = evaluation;
            DataTable eves = new DataTable();
            PropertyInfo[] properties = typeof(Evaluation).GetProperties();
            foreach (PropertyInfo property in properties)
            {
                try
                {
                    EvaluationXMLPathLookUp item = lookups.Find(x => x.FieldName.Equals(property.Name));
                    if (item != null)
                    {
                        if (item.XMLPath != null)
                        {
                            string value = XMLHelper.GetXmlTsvValue(item.XMLPath, h2k, item.Attribute);
                            if (value != null)
                            {
                                if (property.PropertyType.FullName.ToLower().Contains("int"))
                                {
                                    if (value.Contains("."))
                                    {
                                        value = value.Substring(0, value.IndexOf("."));
                                    }

                                    property.SetValue(eval, Convert.ToInt32(value.Equals("") ? null : value));
                                }
                                else if (property.PropertyType.FullName.ToLower().Contains("dec"))
                                {
                                    property.SetValue(eval, Convert.ToDecimal(value.Equals("") ? null : value));
                                }
                                else if (property.PropertyType.FullName.ToLower().Contains("date"))
                                {                                    
                                    property.SetValue(eval, Convert.ToDateTime(value));
                                }
                                else if (property.PropertyType.FullName.ToLower().Contains("char"))
                                {
                                    property.SetValue(eval, Convert.ToChar( string.IsNullOrEmpty(value)?string.Empty : value.Substring(0,1)));
                                }
                                else
                                {
                                    
                                    property.SetValue(eval, value.ToUpper().Equals("TRUE") ? "T" : value.ToUpper().Equals("FALSE") ? "F" : value.Equals("0.0") ? "0" : value);
                                }
                            }
                        }
                    }
                    eves.Columns.Add(property.Name, Nullable.GetUnderlyingType(
                            property.PropertyType) ?? property.PropertyType);
                }
                catch (Exception e)
                {
                    Console.WriteLine(property.Name);
                }
            }




            DataRow neweve = eves.NewRow();
            foreach (PropertyInfo property in properties)
            {
                neweve[property.Name] = evaluation.GetType().GetProperty(property.Name).GetValue(eval, null) ?? DBNull.Value;
            }
            eves.Rows.Add(neweve);
            return eves;
        }
        public static DataTable CreateRunLogDataTable(List<RunLog> runLogs, int evaluationsId)
        {
            DataTable logDT = new DataTable();
            PropertyInfo[] properties = typeof(RunLog).GetProperties();
            foreach (PropertyInfo property in properties)
            {

                logDT.Columns.Add(property.Name, Nullable.GetUnderlyingType(
                        property.PropertyType) ?? property.PropertyType);

            }

            foreach (RunLog rl in runLogs)
            {
                rl.EvaluationsId = evaluationsId;
                DataRow newLog = logDT.NewRow();
                foreach (PropertyInfo property in properties)
                {
                    newLog[property.Name] = rl.GetType().GetProperty(property.Name).GetValue(rl, null) ?? DBNull.Value;
                }
                logDT.Rows.Add(newLog);
            }
            return logDT;
        }

        public static DataTable CreateERSDataTable(ERSExtention ers, XmlDocument h2k, List<EvaluationXMLPathLookUp> lookups)
        {            
            DataTable eves = new DataTable();

            PropertyInfo[] properties = typeof(ERSExtention).GetProperties();
            foreach (PropertyInfo property in properties)
            {
                try
                {
                    EvaluationXMLPathLookUp item = lookups.Find(x => x.FieldName.Equals(property.Name));
                    if (item != null)
                    {
                        if (item.XMLPath != null)
                        {
                            string value = XMLHelper.GetXmlTsvValue(item.XMLPath, h2k, item.Attribute);
                            if (value != null)
                            {
                                if (property.PropertyType.FullName.ToLower().Contains("int"))
                                {
                                    property.SetValue(ers, Convert.ToInt32(value.Equals("") ? null : value));
                                }
                                else if (property.PropertyType.FullName.ToLower().Contains("dec"))
                                {
                                    property.SetValue(ers, Convert.ToDecimal(value.Equals("") ? null : value));
                                }
                                else if (property.PropertyType.FullName.ToLower().Contains("date"))
                                {
                                    property.SetValue(ers, Convert.ToDateTime(value));
                                }
                                else
                                {

                                    property.SetValue(ers, value.ToUpper().Equals("TRUE") ? "T" : value.ToUpper().Equals("FALSE") ? "F" : value.Equals("0.0") ? "0" : value);
                                }
                            }
                        }
                    }
                    eves.Columns.Add(property.Name, Nullable.GetUnderlyingType(
                            property.PropertyType) ?? property.PropertyType);
                }
                catch (Exception e)
                {
                    Console.WriteLine(property.Name);
                }
            }

            DataRow neweve = eves.NewRow();
            foreach (PropertyInfo property in properties)
            {
                neweve[property.Name] = ers.GetType().GetProperty(property.Name).GetValue(ers, null) ?? DBNull.Value;
            }
            eves.Rows.Add(neweve);
            return eves;
        }
    }
}
