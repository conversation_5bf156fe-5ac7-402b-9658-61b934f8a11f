﻿using System;
using System.Xml;
using System.Collections.Generic;
using System.Text;
using System.Globalization;

namespace EvaluationValidationEcosystemFunction.Tasks.Helper
{
    public class XMLHelper
    {
        public static string GetXmlTsvValue(string fieldname, XmlDocument h2k, int Attribute = 0)
        {
            
            string value = null;

            if (string.IsNullOrEmpty(fieldname))
                return value;

            XmlNode ruleNode = h2k.SelectSingleNode(fieldname);
            
            if (!(ruleNode is null))
            {
                if (Attribute == -1)
                {
                    value = string.IsNullOrEmpty(Convert.ToString(ruleNode.InnerText)) ? Convert.ToString(ruleNode.InnerXml) : Convert.ToString(ruleNode.InnerText);
                }
                else if (ruleNode != null)
                {
                    if (fieldname.ToLower().Contains("date"))
                    {

                        value = DateTime.ParseExact(Convert.ToString(ruleNode.Attributes[Attribute].InnerText).Equals("") ? null : Convert.ToString(ruleNode.Attributes[Attribute].InnerText), "dd/MM/yyyy", CultureInfo.InvariantCulture).ToString("yyyy-MM-dd");
                    }
                    else
                    {
                        value = Convert.ToString(ruleNode.Attributes[Attribute].InnerText);
                    }
                }
            }
            return value;
        }
    }
}
