﻿using System;
using System.Globalization;

namespace EvaluationValidationEcosystemFunction.Tasks.Formatters
{
    public class CustomFormatter : IFormatProvider, ICustomFormatter
    {

        public string Format(string format,
                         object arg,
                         IFormatProvider formatProvider)
        {

            if (!this.Equals(formatProvider))
            {
                return null;
            }        
            if (arg is IFormattable)
                return ((IFormattable)arg).ToString(format, CultureInfo.CurrentCulture);
            else if (arg != null)
                return arg.ToString();
            else
                return String.Empty;
         
        }
        public object GetFormat(Type formatType)
        {
            if (formatType == typeof(ICustomFormatter))
                return this;
            else
                return null;
        }
    }
}