﻿using System;

namespace EvaluationValidationEcosystemFunction.Tasks.Formatters
{
    public class PostalCodeFormatter : IFormatProvider, ICustomFormatter
    {      
        public string Format(string format,
                        object arg,
                        IFormatProvider formatProvider)
        {

            if (!this.Equals(formatProvider))
            {
                return null;
            }
            string customerString = arg.ToString() as string;
            if (customerString.Length == 6)
            {
                string len1 = customerString.Substring(0, Math.Min(3, customerString.Length));
                string len2 = customerString.Substring(Math.Min(3, customerString.Length));
                return string.Format(string.Format("{0,3} {0,3}", len1, len2));
            }
            return customerString;
        }
        public object GetFormat(Type formatType)
        {
            if (formatType == typeof(ICustomFormatter))
                return this;
            else
                return null;
        }
    }
}
