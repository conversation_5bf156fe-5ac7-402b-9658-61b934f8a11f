﻿using EvaluationValidationEcosystemFunction.Data.Repository.H2KFile;
using EvaluationValidationEcosystemFunction.Data.Repository.Rule;
using EvaluationValidationEcosystemFunction.Domain.Evaluation;
using EvaluationValidationEcosystemFunction.Domain.Request;
using EvaluationValidationEcosystemFunction.Domain.Response;
using EvaluationValidationEcosystemFunction.Domain.Rule;
using EvaluationValidationEcosystemFunction.Tasks.Helper;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml;


namespace EvaluationValidationEcosystemFunction.Tasks
{
    public class RunRules : IRunRules
    {
        private readonly IEvaluationValidationRepository _evalRepo;
        private readonly IH2KFileRepository _h2kRepo;

        public RunRules(IEvaluationValidationRepository evalRepo, IH2KFileRepository h2kRepo)
        {
            this._evalRepo = evalRepo;
            this._h2kRepo = h2kRepo;
        }
        public async Task<RulesResult> GetInitialSubmissionRuleResults(EvaluationFile eval, string language)
        {
            string h2kFile = await _h2kRepo.GetH2KFileAsync(eval);
			//var h2kFile = System.IO.File.ReadAllText(@"C:\Users\<USER>\Downloads\98G6D00041.h2k");

			XmlDocument h2k = new XmlDocument();
            h2k.LoadXml(h2kFile);
            
            //Parse initial portions of the XML for use in calling DB for Rules
            string program = XMLHelper.GetXmlTsvValue("//HouseFile/Program/Labels/English", h2k, -1);
            string version = XMLHelper.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/ProgramName", h2k, 0);
            DateTime evaluationDate = Convert.ToDateTime(XMLHelper.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/EntryDate", h2k, 0));
            string fileIdentifier = XMLHelper.GetXmlTsvValue("//HouseFile/ProgramInformation/File/Identification", h2k, -1).Trim();

            string fileType = fileIdentifier.Substring(4, 1);

            EvaluationType evalType = new EvaluationType();
            evalType.Program = program;
            evalType.FileType = fileType;
            evalType.FileVersion = version;
            evalType.SubmissionType = eval.SubmissionType;

            RulesResult rulesResult = new RulesResult();
            if (!(h2k.SelectNodes("//HouseFile/Program/Results/Tsv").Count > 0))
            {
                RunLog runLog = new RunLog(evaluationDate, program, fileType, version, fileIdentifier, eval.EvaluationFileBatchId);
                runLog.RuleDefinitionResult = "Rejected";
                runLog.FieldValue = null;
                runLog.Message = language.ToLower().Equals("fr") ? $"Aucune pièce jointe valide détectée. Veuillez sélectionner le mode Système de cote ÉnerGuide ou le mode ERS Maison de référence de l’Ontario et soumettre à nouveau votre fichier." : $"No valid attachment detected. Please select either the EnerGuide Rating System or ERS Ontario Reference House mode and re-submit your file.";
                runLog.MessageFr = $"Aucune pièce jointe valide détectée. Veuillez sélectionner le mode Système de cote ÉnerGuide ou le mode ERS Maison de référence de l’Ontario et soumettre à nouveau votre fichier.";
                runLog.MessageEn = $"No valid attachment detected. Please select either the EnerGuide Rating System or ERS Ontario Reference House mode and re-submit your file.";
                runLog.RuleDefinitionId = -1;
                runLog.RunDateTime = DateTime.Now;
                rulesResult.RunLog.Add(runLog);
                rulesResult.EvaluationStatus = runLog.RuleDefinitionResult;
                rulesResult.FileType = fileType;
                rulesResult.EvaluationFile = eval;
            }
            else
            {
                DateTime starttime = DateTime.Now;
                EvaluationType file = _evalRepo.GetRules(evalType, language);
                Console.WriteLine("Get Rules: " + Convert.ToString(DateTime.Now - starttime));
                starttime = DateTime.Now;
                rulesResult = RunRulesForFile(file, h2k, fileIdentifier, evaluationDate, eval, language);
                Console.WriteLine("Run Rules: " + Convert.ToString(DateTime.Now - starttime));
                rulesResult.FileType = fileType;
            }
            return rulesResult;
        }

        protected RulesResult RunRulesForFile(EvaluationType file, XmlDocument h2k, string fileIdentifier, DateTime evaluationDate, EvaluationFile eval, string language)
        {
            RulesResult rulesResult = new RulesResult();
            List<RunLog> runLogs = new List<RunLog>();
            Evaluation evaluation = new Evaluation();
            rulesResult.Evaluation = evaluation;

            string previousFileId = null;
            string program =    "";
            string fileType =   "";
            string version = "";
            int evaluationsId = -1;

            if (file is null)
            {
                try
                {
                    RunLog runLog = new RunLog(evaluationDate, program, fileType, version, fileIdentifier, eval.EvaluationFileBatchId);
                    runLog.RuleDefinitionResult = "Rejected";
                    runLog.FieldValue = "HouseFile";
                    runLog.Message = language.ToLower().Equals("fr") ? "Le numéro de fichier n’est pas valide. Seuls les fichiers D/E/P/N peuvent être soumis au portail de soumission de fichiers ERS." : "The file number is invalid. Only D/E/P/N files can be submitted to the ERS file submission portal.";
                    runLog.MessageFr = "Le numéro de fichier n’est pas valide.Seuls les fichiers D/ E / P / N peuvent être soumis au portail de soumission de fichiers ERS.";
                    runLog.MessageEn = "The file number is invalid. Only D/E/P/N files can be submitted to the ERS file submission portal.";
                    runLog.RuleDefinitionId = -1;
                    runLog.RunDateTime = DateTime.Now;
                    runLogs.Add(runLog);
                    rulesResult.EvaluationStatus = "Rejected";
                }
                catch (Exception e)
                {
                    rulesResult.Logs.Add(e.Message);
                }
            }
            else
            {
                evaluation.EVALTYPE = file.FileType;
                evaluation.FILENUMBER = fileIdentifier;
                evaluation.CREATIONDATE = DateTime.Now;
                evaluation.MODIFICATIONDATE = DateTime.Now;
                evaluation.BatchId = eval.EvaluationFileBatchId;
                evaluation.EVALUATOR = fileIdentifier.Substring(2, 2);
                evaluation.PARTNER = fileIdentifier.Substring(0, 2);
                evaluation.IDNUMBER = fileIdentifier.Substring(5, 5);
                                
                program = file.Program ?? "";
                fileType = file.FileType ?? "";
                version = file.FileVersion ?? "";

                string builder = XMLHelper.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/Builder", h2k, 0);
                if (eval.EvaluationFileName.ToUpper().Replace(".H2K", "").Equals(builder.ToUpper()))
                {
                    RunLog runLog = new RunLog(evaluationDate, program, fileType, version, fileIdentifier, eval.EvaluationFileBatchId);
                    runLog.RuleDefinitionResult = "Processed";
                    runLog.FieldValue = builder;
                    runLog.Message = language.ToLower().Equals("fr") ? $"Le numéro de fichier ({builder}) précisé dans le fichier *.TSV ne correspond pas à l'identification du fichier ({eval.EvaluationFileName})." : $"The file number ({builder}) specified in the *.TSV does not match the file ID ({eval.EvaluationFileName}).";
                    runLog.MessageFr = $"Le numéro de fichier ({builder}) précisé dans le fichier *.TSV ne correspond pas à l'identification du fichier ({eval.EvaluationFileName}).";
                    runLog.MessageEn = $"The file number ({builder}) specified in the *.TSV does not match the file ID ({eval.EvaluationFileName}).";
                    runLog.RuleDefinitionId = -1;
                    runLog.RunDateTime = DateTime.Now;
                    runLogs.Add(runLog);
                }
                else
                {
                    RunLog runLog = new RunLog(evaluationDate, program, fileType, version, fileIdentifier, eval.EvaluationFileBatchId);
                    runLog.RuleDefinitionResult = "Rejected";
                    runLog.FieldValue = builder;
                    runLog.Message = language.ToLower().Equals("fr") ? $"Le numéro de fichier ({builder}) précisé dans le fichier *.TSV ne correspond pas à l'identification du fichier ({eval.EvaluationFileName})." : $"The file number ({builder}) specified in the *.TSV does not match the file ID ({eval.EvaluationFileName}).";
                    runLog.MessageFr = $"Le numéro de fichier ({builder}) précisé dans le fichier *.TSV ne correspond pas à l'identification du fichier ({eval.EvaluationFileName}).";
                    runLog.MessageEn = $"The file number ({builder}) specified in the *.TSV does not match the file ID ({eval.EvaluationFileName}).";
                    runLog.RuleDefinitionId = -1;
                    runLog.RunDateTime = DateTime.Now;
                    runLogs.Add(runLog);
                }


                if (file.RuleDefinitions is null || file.RuleDefinitions.Count < 1)
                {
                    try
                    {
                        RunLog runLog = new RunLog(evaluationDate, program, fileType, version, fileIdentifier, eval.EvaluationFileBatchId);
                        runLog.RuleDefinitionResult = "Rejected";
                        runLog.FieldValue = "HouseFile";
                        runLog.Message = language.ToLower().Equals("fr") ? $"L'évaluation {fileIdentifier} a été créée à l'aide d'une version invalide du logiciel ({version})." : $"The evaluation {fileIdentifier} was created using an invalid version of the software ({version}).";
                        runLog.MessageFr = $"L'évaluation {fileIdentifier} a été créée à l'aide d'une version invalide du logiciel ({version}).";
                        runLog.MessageEn = $"The evaluation {fileIdentifier} was created using an invalid version of the software ({version}).";                                            	                            
                        runLog.RuleDefinitionId = -1;
                        runLog.RunDateTime = DateTime.Now;
                        runLogs.Add(runLog);
                    }
                    catch (Exception e)
                    {
                        rulesResult.Logs.Add(e.Message);
                    }
                }
                else
                {
                    if (file.FileType.Equals("E") && string.IsNullOrEmpty(XMLHelper.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/PreviousFileID", h2k, 0)))
                    {
                        previousFileId = evaluation.PARTNER + evaluation.EVALUATOR + "D" + evaluation.IDNUMBER;
                        XmlNode myNode = h2k.SelectSingleNode("//HouseFile/Program/Results/Tsv/PreviousFileID");
                        myNode.Attributes[0].InnerText = previousFileId;
                    }

                    List<RuleDefinition> ruleOrders = file.RuleDefinitions.GroupBy(x => x.RuleOrder).Select(g => g.FirstOrDefault()).OrderBy(y => y.RuleOrder).ToList();
                    List<RuleDefinition> MessageLookUps = file.RuleDefinitions.Where(x => x.RuleType.ToUpper().Equals("MESSAGELOOKUP")).ToList();

                    foreach (RuleDefinition ruleOrder in ruleOrders)
                    {
                        
                        // For MultiThreading
                        List<Task<RunLog>> runRules = new List<Task<RunLog>>();
                        foreach (RuleDefinition rule in file.RuleDefinitions.Where(x => x.RuleOrder == ruleOrder.RuleOrder && !(x.RuleType.ToUpper().Equals("MESSAGELOOKUP")) && (x.RuleType.ToUpper().Equals("REGULAR") || x.RuleType.ToUpper().Equals("REGEX"))))
                        {
							RunLog runLog = new RunLog(evaluationDate, program, fileType, version, fileIdentifier, eval.EvaluationFileBatchId);

                            //For MultiThreading
							runRules.Add(Task<RunLog>.Factory.StartNew(() => RunRule(rule, runLog, h2k, file.EvaluationXMLPathLookUps, MessageLookUps, fileIdentifier, previousFileId)));
                        }
                        //For MultiThreading
                        Task.WaitAll(runRules.ToArray());

                        foreach (RuleDefinition rule in file.RuleDefinitions.Where(x => x.RuleOrder == ruleOrder.RuleOrder && !(x.RuleType.ToUpper().Equals("MESSAGELOOKUP")) && !(x.RuleType.ToUpper().Equals("REGULAR") || x.RuleType.ToUpper().Equals("REGEX"))))
                        {
                            RunLog runLog = new RunLog(evaluationDate, program, fileType, version, fileIdentifier, eval.EvaluationFileBatchId);

                            //No Multithreading
                            RunLog runLogResult = new RunLog(evaluationDate, program, fileType, version, fileIdentifier, eval.EvaluationFileBatchId);
                            runLogResult = RunRule(rule, runLog, h2k, file.EvaluationXMLPathLookUps, MessageLookUps, fileIdentifier, previousFileId);
                            runLogs.Add(runLogResult);
                            if (runLogResult.RuleRunError != null)
                            {
                                rulesResult.Logs.Add(runLogResult.RuleRunError);
                            }
                        }

                        foreach (Task<RunLog> rl in runRules)
                        {
                            runLogs.Add(rl.Result);
                            if (rl.Result.RuleRunError != null)
                            {
                                rulesResult.Logs.Add(rl.Result.RuleRunError);
                            }
                        }


                        RunLog runLogReject = runLogs.FindLast(x => x.RuleDefinitionResult.Equals("Rejected"));

                        if (!(runLogReject is null))
                        {
                            rulesResult.EvaluationStatus = "Rejected";
                            break;
                        }
                        else
                        {
                            rulesResult.EvaluationStatus = "Draft";
                        }
                    }
                    //For debugging run one rule at a time
                   // RunLog runLog1 = new RunLog(evaluationDate, program, fileType, version, fileIdentifier, eval.EvaluationFileBatchId);
                    //RunRule(file.RuleDefinitions.Find( x => x.RuleDefinitionId == 1256), runLog1, h2k, file.EvaluationXMLPathLookUps, MessageLookUps, fileIdentifier, previousFileId);
                    DateTime start = DateTime.Now;

                    rulesResult.EvaluationIncentiveRecommendations = new List<EvaluationIncentiveRecommendation>();
                    foreach (RunLog log in runLogs)
                    {
                        if (log.RuleDefinitionResult.Equals("Recommended"))
                        {
                            RuleDefinition def = file.RuleDefinitions.FindLast(x => x.RuleDefinitionId == log.RuleDefinitionId);

                            rulesResult.EvaluationIncentiveRecommendations.Add(new EvaluationIncentiveRecommendation(def.IncentiveLabelEn, Convert.ToInt32(XMLHelper.GetXmlTsvValue(def.ResultLookUpField, h2k, 1) ?? "99")));
                        }
                    }
                    if (file.FileType.Equals("D") && XMLHelper.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/GreenerHomes", h2k, 0) == "1" && rulesResult.EvaluationIncentiveRecommendations.Count == 0)
                    {
                        try
                        {
                            rulesResult.EvaluationStatus = "Rejected";
                            RunLog runLog = new RunLog(evaluationDate, program, fileType, version, fileIdentifier, eval.EvaluationFileBatchId);
                            runLog.RuleDefinitionResult = "Rejected";
                            runLog.FieldValue = "HouseFile";
                            runLog.Message = language.ToLower().Equals("fr") ? $"Votre fichier D ne contient aucune recommandation. Veuillez modéliser votre fichier correctement pour afficher les recommandations." : $"Your D file has no recommendation(s). Please remodel your file correctly to display recommendations.";
                            runLog.MessageFr = $"Votre fichier D ne contient aucune recommandation. Veuillez modéliser votre fichier correctement pour afficher les recommandations.";
                            runLog.MessageEn = $"Your D file has no recommendation(s). Please remodel your file correctly to display recommendations.";
                            runLog.RuleDefinitionId = -2;                      
                            runLog.RunDateTime = DateTime.Now;
                            runLogs.Add(runLog);
                        }
                        catch (Exception e)
                        {
                            rulesResult.Logs.Add(e.Message);
                        }
                    }
                    Evaluation evalDT = new Evaluation();

                    evaluation.EVALUATIONSTATUS = rulesResult.EvaluationStatus;
                    DataTable eves = DataTableHelper.CreateEvaluationDataTable(evaluation, h2k, file.EvaluationXMLPathLookUps, out evalDT);
                    
                    try
                    {
                        evaluationsId = _evalRepo.SetEvaluation(eves, eval.SubmissionType);
                        ERSExtention ersExt = new ERSExtention();
                        if (evaluationsId != -1 && !eval.SubmissionType.Equals("U"))
                        {
                            ersExt.EVAL_ID = evaluationsId;
                            ersExt.EVAL_TYPE = fileType;
                            ersExt.FILE_NAME = eval.EvaluationFileName;
                            DataTable ers = DataTableHelper.CreateERSDataTable(ersExt, h2k, file.EvaluationXMLPathLookUps);
                            _evalRepo.SetERSExtention(ers, eval.SubmissionType);
                        }
                    }
                    catch (Exception e)
                    {
                        rulesResult.Logs.Add("Set Evaluation Error: " + e.Message);
                    }
                    Console.WriteLine("Set Eval is complete in " + (DateTime.Now - start).TotalMilliseconds.ToString());
                    //if (evaluationsId == -1 && !eval.SubmissionType.Equals("U"))
                    //{
                    //    evaluation.EVALUATIONSTATUS = "Rejected";
                    //    rulesResult.EvaluationStatus = "Rejected";
                    //    RunLog runLog = new RunLog(evaluationDate, program, fileType, version, fileIdentifier, eval.EvaluationFileBatchId);
                    //    runLog.RuleDefinitionResult = "Rejected";
                    //    runLog.FieldValue = "HouseFile";
                    //    runLog.Message = language.ToLower().Equals("fr") ? "Le fichier a déjà été soumis. Veuillez soumettre en tant que mise à jour si le fichier nécessite des modifications." : "File has already been submitted. Please submit as an update if the file requires changes.";
                    //    runLog.MessageFr = "Le fichier a déjà été soumis. Veuillez soumettre en tant que mise à jour si le fichier nécessite des modifications.";
                    //    runLog.MessageEn = "File has already been submitted. Please submit as an update if the file requires changes.";
                    //    runLog.RuleDefinitionId = -1;
                    //    runLog.RunDateTime = DateTime.Now;
                    //    runLogs.Add(runLog);
                    //}
                    evalDT.EVALUATIONSID = evaluationsId;
                    rulesResult.Evaluation = evalDT;
                }                
            }
            DateTime starts = DateTime.Now;
            int batchId = -1;
            try
            {
                DataTable logDT = DataTableHelper.CreateRunLogDataTable(runLogs, evaluationsId);
                batchId = _evalRepo.SetLookUpRuleResults(logDT, evaluationsId);
            }
            catch (Exception ex)
            {
                rulesResult.Logs.Add("Set Log Error: " + ex.Message);
            }
            Console.WriteLine("Set RunLog is complete in " + (DateTime.Now - starts).TotalMilliseconds.ToString());
            rulesResult.BatchId = batchId;            
            rulesResult.Evaluation.BatchId = batchId;

            for (int i = 0; i<runLogs.Count; i++)
            {
                runLogs[i].BatchId = batchId;
            }
            rulesResult.RunLog = runLogs;

            return rulesResult;
        }

        protected async Task<bool> RuleCompare(RuleDefinition rule, int compareGroup, XmlDocument h2k, string fileNumber, string previousFileId)
        {
            List<RuleDefinitionCompare> compareRules = new List<RuleDefinitionCompare>();
            compareRules.AddRange(rule.RuleDefinitionCompares);
            int counter = 1;
            bool check = true;
            bool positiveResultStatement = true;
            try
            {
                foreach (RuleDefinitionCompare defCompare in compareRules)
                {
                    int compareCheck = defCompare.CheckLevel;
                    if (compareCheck != counter)
                    {
                        if (!check)
                        {
                            break;
                        }
                        counter++;
                    }

                    positiveResultStatement = defCompare.PositiveResultValue;
                    string dependancyType = defCompare.DependancyType ?? "AND";
                    string fieldOneAdditionalLogic = string.IsNullOrEmpty(defCompare.FieldOneAdditionalLogic) ?
                        "" : defCompare.FieldOneAdditionalLogic.ToUpper();
                    string compareOperator = defCompare.Operator;
                    string fieldTwoValue = defCompare.FieldTwoValue;
                    string fieldTwoAdditionalLogic = string.IsNullOrEmpty(defCompare.FieldTwoAdditionalLogic) ? 
                        "" : defCompare.FieldTwoAdditionalLogic.ToUpper();
                    string fieldTwoType = defCompare.FieldTwoType;
                    string fieldName = defCompare.FieldName;
                    int attribute = defCompare.Attribute;
                    string fieldValue = fieldName.ToUpper().Contains("//") ? XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute) : fieldName;
                    bool currentCheck = false;
                    
                    if (fieldTwoAdditionalLogic.Contains("CHECKIFANY") || fieldOneAdditionalLogic.Contains("CHECKIFANY"))
                    {
                        currentCheck = CheckIfAny(defCompare, h2k);
                    } 
                    else if (fieldTwoAdditionalLogic.Contains("CHECKEACHVALUE") || fieldOneAdditionalLogic.Contains("CHECKEACHVALUE"))
                    {
                        currentCheck = CompareEachValue(defCompare, h2k);
                    }
                    else if (fieldTwoAdditionalLogic.Contains("SUMEACHVALUE") || fieldOneAdditionalLogic.Contains("SUMEACHVALUE"))
                    {
                        currentCheck = SumEachValue(defCompare, h2k);
                    }
                    // Stephen Hong ticket 34977: replaced SUMSET logic to return a decimal instead of a boolean
                    //else if (fieldTwoAdditionalLogic.Contains("SUMSET") || fieldOneAdditionalLogic.Contains("SUMSET"))
                    //{
                    //    currentCheck = SumSet(defCompare, h2k);
                    //}
                    else if (fieldTwoAdditionalLogic.Contains("PREVIOUSCOMPAREEACHVALUE") || fieldOneAdditionalLogic.Contains("PREVIOUSCOMPAREEACHVALUE"))
                    {
                        currentCheck = PreviousCompareEachValue(defCompare, h2k);
                    }
                    else if (fieldTwoAdditionalLogic.Contains("ASHPMODELCHECK") || fieldOneAdditionalLogic.Contains("ASHPMODELCHECK"))
                    {
                        currentCheck = AshpModelCheck(h2k);
                    }
                    else if (fieldTwoAdditionalLogic.Contains("BASEMENTPERIMETERCHECK") || fieldOneAdditionalLogic.Contains("BASEMENTPERIMETERCHECK"))
                    {
                        currentCheck = BasementPerimeterCheck(h2k);
                    }
                    else if (fieldTwoAdditionalLogic.Contains("CEILINGAREACHECK") || fieldOneAdditionalLogic.Contains("CEILINGAREACHECK"))
                    {
                        currentCheck = CeilingAreaCheck(h2k);
                    }
                    else if (fieldTwoAdditionalLogic.Contains("CEILINGINSULATIONCHECK") || fieldOneAdditionalLogic.Contains("CEILINGINSULATIONCHECK"))
                    {
                        currentCheck = CeilingInsulationCheck(h2k);
                    }
                    else if (fieldTwoAdditionalLogic.Contains("FANMODELCHECK") || fieldOneAdditionalLogic.Contains("FANMODELCHECK"))
                    {
                        currentCheck = FanModelCheck(h2k);
                    }
                    else if (fieldTwoAdditionalLogic.Contains("WINDOWCHECK") || fieldOneAdditionalLogic.Contains("WINDOWCHECK"))
                    {
                        currentCheck = await WindowCheck(h2k);
                    }
                    else if (fieldOneAdditionalLogic.Contains("STRINGCOMPARE"))
                    {
                        currentCheck = StringCompare(rule, defCompare, h2k, fileNumber);
                    }
                    else
                    {
                        if (fieldTwoAdditionalLogic.Contains("PREVIOUSVALUELOOKUP"))
                        {
                            fieldTwoValue = await PreviousValueLookUp(Convert.ToInt32(defCompare.FieldTwoAdditionalLogic.Split(" ")[1]), h2k, defCompare.FieldTwoAdditionalLogic, defCompare.FieldTwoValue, defCompare.Attribute);
                            fieldTwoAdditionalLogic = "";
                        }

                        if (fieldOneAdditionalLogic.Contains("PREVIOUSVALUELOOKUP"))
                        {
                            fieldValue = await PreviousValueLookUp(Convert.ToInt32(defCompare.FieldOneAdditionalLogic.Split(" ")[1]), h2k, defCompare.FieldOneAdditionalLogic, defCompare.FieldName, defCompare.Attribute);
                            fieldOneAdditionalLogic = "";
                        }

                        if (fieldTwoAdditionalLogic.Contains("CURRENTVALUELOOKUP"))
                        {

                            fieldTwoValue = await CurrentValueLookUp(Convert.ToInt32(defCompare.FieldTwoAdditionalLogic.Split(" ")[1]), h2k, defCompare.FieldTwoAdditionalLogic, defCompare.FieldTwoValue, defCompare.Attribute);
                            fieldTwoAdditionalLogic = "";
                        }

                        if (fieldOneAdditionalLogic.Contains("CURRENTVALUELOOKUP"))
                        {
                            fieldValue = await CurrentValueLookUp(Convert.ToInt32(defCompare.FieldOneAdditionalLogic.Split(" ")[1]), h2k, defCompare.FieldOneAdditionalLogic, defCompare.FieldName, defCompare.Attribute);
                            fieldOneAdditionalLogic = "";
                        }

                        if (fieldTwoAdditionalLogic.Contains("COUNTTAGS"))
                        {
                            fieldTwoValue = CountTags(defCompare.FieldTwoValue, h2k);
                            fieldTwoAdditionalLogic = "";
                        }

                        if (fieldOneAdditionalLogic.Contains("COUNTTAGS"))
                        {
                            fieldValue = CountTags(defCompare.FieldName, h2k);
                            fieldOneAdditionalLogic = "";
                        }

                        if (fieldTwoAdditionalLogic.Contains("LOOKUP"))
                        {
                            fieldTwoValue = LookUp(h2k, rule, defCompare.RuleDefinitionId, fileNumber, previousFileId) ?? "";
                            fieldTwoAdditionalLogic = "";
                        }

                        if (fieldOneAdditionalLogic.Contains("LOOKUP"))
                        {
                            fieldValue = LookUp(h2k, rule, defCompare.RuleDefinitionId, fileNumber, previousFileId) ?? "";
                            fieldOneAdditionalLogic = "";
                        }

                        if (fieldOneAdditionalLogic.Contains("MATH"))
                        {
                            fieldValue = Math(fieldValue, fieldOneAdditionalLogic, h2k);
                            fieldOneAdditionalLogic = "";
                        }

                        if (fieldOneAdditionalLogic.Contains("EXPRESSION:"))
                        {
                            fieldValue = Expression(defCompare, h2k, "EXPRESSION:").ToString();
                            fieldOneAdditionalLogic = "";
                        }

                        if (fieldOneAdditionalLogic.Contains("EXPRESSIONCHECK:"))
                        {
                            fieldValue = ExpressionCheck(defCompare, h2k, "EXPRESSIONCHECK:").ToString();
                            fieldOneAdditionalLogic = "";
                        }

                        if (fieldOneAdditionalLogic.Contains("SUMSET"))
                        {
                            fieldValue = SumSet(defCompare, h2k, "FieldOne");
                            fieldOneAdditionalLogic = "";
                        }

                        if (fieldTwoAdditionalLogic.Contains("SUMSET"))
                        {
                            fieldTwoValue = SumSet(defCompare, h2k, "FieldTwo");
                            fieldTwoAdditionalLogic = "";
                        }

                        // Verify that fieldTwoValue is actually an XML path, as it could have been
                        // replaced with an actual value before reaching this point.
                        if (fieldTwoType.Equals("XML") && fieldTwoValue.StartsWith("//"))
                        {
                            fieldTwoValue = XMLHelper.GetXmlTsvValue(fieldTwoValue, h2k, attribute);
                        }

                        DataTable table = new DataTable();

                        decimal decValueTwo;
                        decimal decValueOne;
                        DataRow[] dr;
                        if (decimal.TryParse(fieldTwoValue, out decValueTwo) && decimal.TryParse(fieldValue, out decValueOne))
                        {
                            if (!(string.IsNullOrEmpty(fieldOneAdditionalLogic)))
                            {
                                DataTable dt = new DataTable();
                                decValueOne = Convert.ToDecimal(dt.Compute(fieldValue + " " + fieldOneAdditionalLogic, ""));
                            }
                            table.Columns.Add("value", typeof(decimal));

                            DataRow r = table.NewRow();
                            r["value"] = decValueOne;
                            table.Rows.Add(r);
                            dr = table.Select(string.Format("value {0} {1} {2}", compareOperator, fieldTwoValue, fieldTwoAdditionalLogic));
                        }
                        else
                        {
                            table.Columns.Add("value", typeof(string));

                            DataRow r = table.NewRow();
                            r["value"] = fieldValue;
                            table.Rows.Add(r);
                            dr = table.Select(string.Format("value {0} '{1}'", compareOperator, fieldTwoValue));
                        }
                        currentCheck = dr.Length > 0;
                    }

                    if (dependancyType.ToUpper().Equals("OR"))
                    {
                        check = check || currentCheck;
                    }
                    else
                    {
                        check = check && currentCheck;
                    }
                }
            }
            catch (Exception e)
            {
                return false;
            }

            if (positiveResultStatement)
            {
                return check;
            }
            else
            {
                return !check;
            }
        }

        protected RunLog RunRule(RuleDefinition rule, RunLog runLog, XmlDocument h2k, List<EvaluationXMLPathLookUp> evalXML, List<RuleDefinition> MessageLookUps,string fileNumber, string previousFileId)
        {
           // runLog.Message = rule.Message.Replace("<fileid>", runLog.FileIdentifier);
            runLog.RuleDefinitionId = rule.RuleDefinitionId;            
            DateTime start = DateTime.Now;
            string evaluationStatus = "Processed";
            string ruleType = rule.RuleType;
            string mandatory = rule.Mandatory;
            string fieldName = rule.FieldName;
            int attribute = rule.Attribute;
            int ruleDefinitionId = rule.RuleDefinitionId;           

            try
            {
                if (ruleType.Equals("Recommendation"))
                {
                    evaluationStatus = "Recommended";
                    if (mandatory.Equals("Yes"))
                    {
                        if (string.IsNullOrEmpty(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute)))
                        {
                            evaluationStatus = "Not Recommended";
                        }
                    }
                    if (!(string.IsNullOrEmpty(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute))))
                    {
                        bool compareTest = RuleCompare(rule, 1, h2k, fileNumber, previousFileId).Result;
                        if (!compareTest)
                        {
                            evaluationStatus = "Not Recommended";
                        }
                    }
                }
                else if (ruleType.Equals("Regular"))
                {
                    string hasMinimum = rule.HasMinimum;
                    string hasMaximum = rule.HasMaximum;

                    if (mandatory.Equals("Yes"))
                    {
                        if (string.IsNullOrEmpty(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute)))
                        {
                            evaluationStatus = "Rejected";
                        }
                    }
                    if (hasMinimum.Equals("Yes"))
                    {
                        if (!(mandatory.Equals("No") && string.IsNullOrEmpty(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute))))
                        {
                            decimal minimumValue = rule.MinimumValue;
                            decimal min;
                            bool minXmlTest = decimal.TryParse(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute), out min);
                            if (minXmlTest)
                            {
                                if (min < minimumValue)
                                {
                                    evaluationStatus = "Rejected";
                                }
                            }
                            else
                            {
                                evaluationStatus = "Rejected";
                            }
                        }
                    }
                    if (hasMaximum.ToString().Equals("Yes"))
                    {
                        if (!(mandatory.Equals("No") && string.IsNullOrEmpty(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute))))
                        {
                            decimal maximumValue = rule.MaximumValue;
                            decimal max;
                            bool maxXmlTest = decimal.TryParse(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute), out max);
                            if (maxXmlTest)
                            {
                                if (max > maximumValue)
                                {
                                    evaluationStatus = "Rejected";
                                }
                            }
                            else
                            {
                                evaluationStatus = "Rejected";
                            }
                        }
                    }
                }
                else if (ruleType.ToString().Equals("Regex"))
                {
                    string regularExpression = rule.RegularExpression;
                    if (mandatory.Equals("Yes"))
                    {
                        if (string.IsNullOrEmpty(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute)))
                        {
                            evaluationStatus = "Rejected";
                        }
                    }
                    if (!(string.IsNullOrEmpty(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute))))
                    {
                        if (!Regex.IsMatch(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute), regularExpression))
                        {
                            evaluationStatus = "Rejected";
                        }
                    }

                }
                else if (ruleType.Equals("LookUpMinimum"))
                {
                    if (mandatory.Equals("Yes"))
                    {
                        if (string.IsNullOrEmpty(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute)))
                        {
                            evaluationStatus = "Rejected";
                        }
                    }
                    if (!(string.IsNullOrEmpty(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute))))
                    {
                        string lookupvalue =  LookUp(h2k, rule, ruleDefinitionId, fileNumber, previousFileId);
                        decimal tryConvert;
                        if (Decimal.TryParse(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute), out tryConvert))
                        {
                            if (Convert.ToDecimal(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute)) < Convert.ToDecimal(lookupvalue))
                            {
                                evaluationStatus = "Rejected";
                            }
                        }
                        else
                        {
                            string item = XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute);
                            int i = string.Compare(item, lookupvalue);
                            if (i < 0)
                            {
                                evaluationStatus = "Rejected";
                            }
                        }
                    }
                }
                else if (ruleType.Equals("LookUpMaximum"))
                {
                    if (mandatory.Equals("Yes"))
                    {
                        if (string.IsNullOrEmpty(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute)))
                        {
                            evaluationStatus = "Rejected";
                        }
                    }
                    if (!(string.IsNullOrEmpty(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute))))
                    {                        
                        string lookupvalue = LookUp(h2k, rule, ruleDefinitionId, fileNumber, previousFileId);

                        decimal tryConvert;
                        if (Decimal.TryParse(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute), out tryConvert))
                        {
                            if (Convert.ToDecimal(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute)) > Convert.ToDecimal(lookupvalue))
                            {
                                evaluationStatus = "Rejected";
                            }
                        }
                        else
                        {
                            string item = XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute);
                            int i = string.Compare(item, lookupvalue);
                            if (i > 0)
                            {
                                evaluationStatus = "Rejected";
                            }
                        }
                    }
                }
                else if (ruleType.Equals("LookUpNotExists"))
                {
                    if (mandatory.Equals("Yes"))
                    {
                        if (string.IsNullOrEmpty(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute)))
                        {
                            evaluationStatus = "Rejected";
                        }
                    }
                    if (!(string.IsNullOrEmpty(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute))))
                    {                        
                        string finalLookupValue = LookUp( h2k,  rule, ruleDefinitionId, fileNumber, previousFileId);
                        if (!string.IsNullOrEmpty(finalLookupValue) )
                        {
                            evaluationStatus = "Rejected";
                        }
                    }
                }
                else if (ruleType.Equals("LookUpExists")) 
                {
                    if (mandatory.Equals("Yes"))
                    {
                        if (string.IsNullOrEmpty(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute)))
                        {
                            evaluationStatus = "Rejected";
                        }
                    }
                    if (!(string.IsNullOrEmpty(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute))))
                    {
                        string finalLookupValue = LookUp(h2k, rule, ruleDefinitionId, fileNumber, previousFileId);

                        if (string.IsNullOrEmpty(finalLookupValue))
                        {
                            evaluationStatus = "Rejected";
                        }
                    }
                }
                else if (ruleType.Equals("Compare"))
                {

                    if (mandatory.Equals("Yes"))
                    {
                        if (string.IsNullOrEmpty(XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute)))
                        {
                            evaluationStatus = "Rejected";
                        }
                    }
                    if (!evaluationStatus.Equals ("Rejected"))
                    {
                        bool compareTest = RuleCompare(rule, 1, h2k, fileNumber, previousFileId).Result;
                        if (!compareTest)
                        {
                            evaluationStatus = "Rejected";
                        }
                    }
                }
            }
            catch (Exception e)
            {
                evaluationStatus = "Rejected";
                runLog.RuleRunError = "Rule Definition Id:" + ruleDefinitionId.ToString() + " Error: " + e.Message;
            }

            runLog.RuleDefinitionResult = evaluationStatus;
            runLog.FieldValue = XMLHelper.GetXmlTsvValue(fieldName, h2k, attribute);
            runLog.FieldValue = runLog.FieldValue!= null && runLog.FieldValue.Length > 500 ? runLog.FieldValue.Substring(0, 500) : runLog.FieldValue;
            runLog.RunDateTime = DateTime.Now;

            
            if (runLog.RuleDefinitionResult.Equals("Rejected"))
            {
                string Message = rule.Message.Replace("<fileid>", runLog.FileIdentifier).Replace("<partner>", runLog.FileIdentifier.Substring(0, 2)).Replace("<evaluator>", runLog.FileIdentifier.Substring(2, 2)).Replace("<idnumber>", runLog.FileIdentifier.Substring(5, 5));
                string MessageEn = rule.ErrorMessageEn.Replace("<fileid>", runLog.FileIdentifier).Replace("<partner>", runLog.FileIdentifier.Substring(0, 2)).Replace("<evaluator>", runLog.FileIdentifier.Substring(2, 2)).Replace("<idnumber>", runLog.FileIdentifier.Substring(5, 5));
                string MessageFr = rule.ErrorMessageFr.Replace("<fileid>", runLog.FileIdentifier).Replace("<partner>", runLog.FileIdentifier.Substring(0, 2)).Replace("<evaluator>", runLog.FileIdentifier.Substring(2, 2)).Replace("<idnumber>", runLog.FileIdentifier.Substring(5, 5));
                List<string> messageReplaces = FindStringOccurrences(Message, "<eval.");
                messageReplaces.AddRange(FindStringOccurrences(Message, "<LOOKUP "));

                foreach (string s in messageReplaces)
                {
                    string name = s.Replace("<LOOKUP ", "").Replace("<eval.", "").Replace(">", "");

                    string replacement = "";
                    if (s.Contains("<LOOKUP "))
                    {

                        replacement = LookUp(h2k, MessageLookUps.Where(x => x.RuleDefinitionId == Convert.ToInt32(name)).First(), Convert.ToInt32(name), fileNumber, previousFileId);
                    }
                    else
                    {
                        name = evalXML.Find(x => x.FieldName.ToUpper().Equals(name.ToUpper())).XMLPath;
                        replacement = XMLHelper.GetXmlTsvValue(name, h2k, 0);
                    }
                    Message = Message.Replace(s, replacement);
                    MessageEn = MessageEn.Replace(s, replacement);
                    MessageFr = MessageFr.Replace(s, replacement);
                }
                runLog.Message = Message;
                runLog.MessageEn = MessageEn;
                runLog.MessageFr = MessageFr;
            }
            else
            {
                runLog.Message = rule.Message.Equals(rule.ErrorMessageEn) ? "Validation successful" : "Validation réussie.";
                runLog.MessageEn = "Validation successful";
                runLog.MessageFr = "Validation réussie.";
            }

            TimeSpan t = Convert.ToDateTime(runLog.RunDateTime) - start;
           // Console.WriteLine(rule.RuleDefinitionId.ToString() + " is Complete in " + t.TotalMilliseconds.ToString() + " ms" );
            return runLog;
        }

        protected static bool CompareEachValue(RuleDefinitionCompare defCompare, XmlDocument h2k)
        {
            bool result = true;
            string[] logic = null;
            string[] values = null;

            if (!(defCompare.FieldOneAdditionalLogic is null))
            {
                logic = defCompare.FieldOneAdditionalLogic.Split(" ");
                string fieldOne = XMLHelper.GetXmlTsvValue(defCompare.FieldName, h2k, defCompare.Attribute);
                values = fieldOne.Split(logic[2]);
            }
            string[] logic2 = null;
            string fieldTwo = defCompare.FieldTwoValue;
            string[] values2 = new string[values.Length];
            if (defCompare.FieldTwoType.Equals("XML"))
            {
                fieldTwo = XMLHelper.GetXmlTsvValue(defCompare.FieldTwoValue, h2k, defCompare.Attribute);
            }

            if (!(defCompare.FieldTwoAdditionalLogic is null))
            {
                logic2 = defCompare.FieldTwoAdditionalLogic.Split(" ");
                values2 = fieldTwo.Split(logic2[2]);
            }
            else
            {
                for (int i = 0; i < values.Length; i++)
                {
                    values2.SetValue(fieldTwo, i);
                }
            }

            if (!(values is null))
            {
                if (logic[1].ToUpper().Equals("EVEN"))
                {
                    for (int i = 0; i < values.Length; i++)
                    {

                        if ((i + 1) % 2 == 0)
                        {
                            System.Data.DataTable table = new System.Data.DataTable();
                            string fieldValue = values[i];
                            string fieldTwoValue = values2[i];
                            decimal decValue;
                            DataRow[] dr;
                            if (decimal.TryParse(fieldTwoValue, out decValue))
                            {

                                table.Columns.Add("value", typeof(decimal));

                                System.Data.DataRow r = table.NewRow();
                                r["value"] = fieldValue;
                                table.Rows.Add(r);
                                dr = table.Select("value " + defCompare.Operator + " " + fieldTwoValue);
                            }
                            else
                            {
                                table.Columns.Add("value", typeof(string));

                                System.Data.DataRow r = table.NewRow();
                                r["value"] = fieldValue;
                                table.Rows.Add(r);
                                dr = table.Select("value " + defCompare.Operator + " '" + fieldTwoValue + "'");
                            }
                            result = result && dr.Length > 0;
                        }

                    }
                }
                else
                {
                    for (int i = 0; i < values.Length; i++)
                    {
                        if ((i + 1) % 2 != 0)
                        {
                            System.Data.DataTable table = new System.Data.DataTable();
                            string fieldValue = values[i];
                            string fieldTwoValue = values2[i];
                            decimal decValue;
                            DataRow[] dr;
                            if (decimal.TryParse(fieldTwoValue, out decValue))
                            {

                                table.Columns.Add("value", typeof(decimal));

                                System.Data.DataRow r = table.NewRow();
                                r["value"] = fieldValue;
                                table.Rows.Add(r);
                                dr = table.Select("value " + defCompare.Operator + " " + fieldTwoValue);
                            }
                            else
                            {
                                table.Columns.Add("value", typeof(string));

                                System.Data.DataRow r = table.NewRow();
                                r["value"] = fieldValue;
                                table.Rows.Add(r);
                                dr = table.Select("value " + defCompare.Operator + " '" + fieldTwoValue + "'");
                            }
                            result = result && dr.Length > 0;
                        }
                    }
                }
            }
            return result;
        }

        /// <summary>
        /// Compares fieldTwo to fieldOne as specified in the StringCompare logic.
        /// <para>Note: defCompare.Operator is ignored for this function.</para>
        /// </summary>
        /// <param name="rule"></param>
        /// <param name="defCompare"></param>
        /// <param name="h2k"></param>
        /// <param name="fileNumber"></param>
        /// <returns></returns>
        protected bool StringCompare(RuleDefinition rule, RuleDefinitionCompare defCompare, XmlDocument h2k, string fileNumber)
        {
            /* This function reads the command in defCompare.FieldOneAdditionalLogic and compares fieldTwo to fieldOne accordingly.
             * The command has the following arguments:
             * - StringCompare: required.
             * - CompareType: optional. See codes below for possible values. Defaults to a direct comparison if not specified.
             * - Delimiter: optional. Used to split fieldOne, fieldTwo, and Ignore into lists. Defaults to "," if not specified.
             * - Ignore: optional. Specifies element(s) to be discarded from fieldOne before comparing. Can contain delimiter.
             * 
             * Example: 'StringCompare CompareType:StartsWith Delimiter:, Ignore:HERX'
             */

            var arguments = new Dictionary<string, string>();
            var command = defCompare.FieldOneAdditionalLogic.Split(" ");
            foreach(var argument in command)
            {
                var a = argument.Split(":");
                // Ignore any parameter not in the format Key:Value
                if (a.Length == 2)
                {
                    arguments.Add(a[0].Trim(), a[1].Trim());
                }
            }

            // If a delimiter was not supplied then default to a space
            if (!arguments.ContainsKey("Delimiter"))
            {
                arguments.Add("Delimiter", ",");
            }

            // Get field one value: can ony be XML value
            var fieldOne = XMLHelper.GetXmlTsvValue(defCompare.FieldName, h2k, defCompare.Attribute);
            List<string> fieldOneValues = fieldOne.Split(arguments["Delimiter"]).Select(item => item.Trim().ToUpper()).ToList();

            // Discard all the values specified in parameter Ignore from fieldOneValues
            if (arguments.ContainsKey("Ignore"))
            {
                var ignoredValues = arguments["Ignore"].Split(arguments["Delimiter"]).Select(item => item.Trim().ToUpper()).ToList();
                fieldOneValues = fieldOneValues.Except(ignoredValues).ToList();
            }

            // Get field two value: can be a text, XML, or LOOKUP value
            var fieldTwo = "";
            if (string.IsNullOrEmpty(defCompare.FieldTwoAdditionalLogic))
            {
                fieldTwo = defCompare.FieldTwoValue;
            }
            else if (defCompare.FieldTwoType.Equals("XML"))
            {
                fieldTwo = XMLHelper.GetXmlTsvValue(defCompare.FieldTwoValue, h2k, defCompare.Attribute);
            }
            else if (defCompare.FieldTwoAdditionalLogic.ToUpper().Contains("LOOKUP"))
            {
                fieldTwo = LookUp(h2k, rule, defCompare.RuleDefinitionId, fileNumber, null) ?? "";
            }

            List<string> fieldTwoValues = fieldTwo.Split(arguments["Delimiter"]).Select(item => item.Trim().ToUpper()).ToList();

            // Perform the action specified in CompareType.
            var result = true;
            if (!arguments.ContainsKey("CompareType"))
            {
                arguments.Add("CompareType", "");
            }

            switch (arguments["CompareType"])
            {
                case "StartsWith":
                    // fieldOne starts with fieldTwo
                    result = string.Join("", fieldOneValues).StartsWith(string.Join("", fieldTwoValues));
                    break;
                case "EndsWith":
                    // fieldOne ends with fieldTwo
                    result = string.Join("", fieldOneValues).EndsWith(string.Join("", fieldTwoValues));
                    break;
                case "ContainsAll":
                    // fieldOne contains all elements of fieldTwo
                    result = !fieldTwoValues.Except(fieldOneValues).ToList().Any();
                    break;
                case "ContainsNone":
                    // fieldOne contains none of the elements of fieldTwo
                    result = !fieldTwoValues.Intersect(fieldOneValues).ToList().Any();
                    break;
                case "ContainsEither":
                    // fieldOne contains at least 1 element of fieldTwo
                    result = fieldTwoValues.Intersect(fieldOneValues).ToList().Any();
                    break;
                default:
                    // fieldOne and fieldTwo both contain the same elements, regardless of ordering
                    result = !fieldOneValues.Except(fieldTwoValues).ToList().Any() && !fieldTwoValues.Except(fieldOneValues).ToList().Any();
                    break;
            }

            return result;
        }

        protected static bool SumEachValue(RuleDefinitionCompare defCompare, XmlDocument h2k)
        {            
            decimal sumResult = SumOfEachValue(defCompare, h2k);

            System.Data.DataTable table = new System.Data.DataTable();
            decimal decValue;
            DataRow[] dr = null;
            if (decimal.TryParse(defCompare.FieldTwoValue, out decValue))
            {

                table.Columns.Add("value", typeof(decimal));

                System.Data.DataRow r = table.NewRow();
                r["value"] = sumResult;
                table.Rows.Add(r);
                dr = table.Select("value " + defCompare.Operator + " " + decValue);
            }
            return dr.Length > 0;            
        }


        protected static decimal SumOfEachValue(RuleDefinitionCompare defCompare, XmlDocument h2k)
        {

            string[] logic = defCompare.FieldOneAdditionalLogic.Split(" ");

            decimal sumResult = 0M;           

            defCompare.FieldName = XMLHelper.GetXmlTsvValue(defCompare.FieldName, h2k, defCompare.Attribute);
            if (defCompare.FieldTwoType.Equals("XML"))
            {
                defCompare.FieldTwoValue = XMLHelper.GetXmlTsvValue(defCompare.FieldTwoValue, h2k, defCompare.Attribute);
            }

            List<RuleDefinitionCompare> compareRules = new List<RuleDefinitionCompare>();

            if (!(defCompare.FieldName is null))
            {
                string[] values;

                if (logic[1].ToUpper().Equals("EVEN"))
                {
                    values = defCompare.FieldName.Split(logic[2]);

                    for (int i = 0; i < values.Length; i++)
                    {
                        RuleDefinitionCompare compare = defCompare;
                        compare.FieldOneAdditionalLogic = null;
                        if ((i + 1) % 2 == 0)
                        {
                            sumResult += Convert.ToDecimal(values[i]);
                        }
                    }
                }
                else
                {
                    values = defCompare.FieldName.Split(logic[2]);

                    for (int i = 0; i < values.Length; i++)
                    {
                        RuleDefinitionCompare compare = defCompare;
                        compare.FieldOneAdditionalLogic = null;
                        if ((i + 1) % 2 != 0)
                        {
                            sumResult += Convert.ToDecimal(values[i]);
                        }
                    }
                }
            }         

            return sumResult;
        }
        private  static decimal ExpressionEvaluate(string expression)
        {
            try
            {
                DataTable table = new System.Data.DataTable();
                table.Columns.Add("expression", string.Empty.GetType(), expression);
                DataRow row = table.NewRow();
                table.Rows.Add(row);
                return decimal.Parse((string)row["expression"]);
            }
            catch (Exception)
            {
                return 0;
            }
        }

        protected static decimal Expression(RuleDefinitionCompare defCompare, XmlDocument h2k, string ruleType)
        {
            string expression = GenerateExpression(defCompare, h2k, ruleType);           
            return ExpressionEvaluate(expression);
        }

        protected static string ExpressionCheck(RuleDefinitionCompare defCompare, XmlDocument h2k, string ruleType)
        {
            string expression = GenerateExpression(defCompare, h2k, ruleType);

            System.Data.DataTable table = new System.Data.DataTable();
            DataRow[] dr;
            table.Columns.Add("value", typeof(bool));
            System.Data.DataRow r = table.NewRow();
            r["value"] = true;
            table.Rows.Add(r);
            dr = table.Select($"value = ({expression})");

            return (dr.Length > 0).ToString();
        }

        private static string  GenerateExpression(RuleDefinitionCompare defCompare, XmlDocument h2k, string ruleType)
        {
            string[] logic = defCompare.FieldOneAdditionalLogic.Split("~");
            string expression = logic[0].Replace(ruleType, string.Empty);
            string[] expressionArgs = logic[1].Substring(5).Split('|');
            string result = string.Empty;
            for (int i = 0; i < expressionArgs.Length; i++)
            {
                string[] internalLogic = expressionArgs[i].Split(' ');
                if (expressionArgs[i].StartsWith("SumOfEachValue"))
                {
                    defCompare.FieldName = internalLogic[4];
                    defCompare.FieldOneAdditionalLogic = expressionArgs[i];
                    result = SumOfEachValue(defCompare, h2k).ToString();
                }
                else if (expressionArgs[i].StartsWith("XML"))
                {
                    defCompare.FieldName = internalLogic[1];
                    result = XMLHelper.GetXmlTsvValue(defCompare.FieldName, h2k, defCompare.Attribute);
                }
                else if (expressionArgs[i].StartsWith("TEXT"))
                {
                    result = internalLogic[1];
                }
                else
                {
                    result = expressionArgs[i];
                }
                expression = expression.Replace("{" + i.ToString() + "}", result);
            }
            return expression;
        }

        public async Task<RulesResult> GetUpdateSubmissionRuleResults(EvaluationFile eval, string language)
        {
            string h2kFile = await _h2kRepo.GetH2KFileAsync(eval);

            // Test
            //string h2kFile = System.IO.File.ReadAllText(@"C:\98SSDTN000 update INFO2.h2k");

            XmlDocument h2k = new XmlDocument();
            h2k.LoadXml(h2kFile);

            //Parse initial portions of the XML for use in calling DB for Rules
            string program = XMLHelper.GetXmlTsvValue("//HouseFile/Program/Labels/English", h2k, -1);
            string version = XMLHelper.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/ProgramName", h2k, 0);
            DateTime evaluationDate = Convert.ToDateTime(XMLHelper.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/EntryDate", h2k, 0));
            string fileIdentifier = XMLHelper.GetXmlTsvValue("//HouseFile/ProgramInformation/File/Identification", h2k, -1);

            string fileType = fileIdentifier.Substring(4, 1);

            EvaluationType evalType = new EvaluationType();
            evalType.Program = program;
            evalType.FileType = fileType;
            evalType.FileVersion = version;
            evalType.SubmissionType = eval.SubmissionType;
            Evaluation evaluation = new Evaluation();

            evaluation.EVALTYPE = fileType;
            evaluation.FILENUMBER = fileIdentifier;
            evaluation.MODIFICATIONDATE = DateTime.Now;
            evaluation.BatchId = eval.EvaluationFileBatchId;
            evaluation.EVALUATOR = fileIdentifier.Substring(2, 2);
            evaluation.PARTNER = fileIdentifier.Substring(0, 2);
            evaluation.IDNUMBER = fileIdentifier.Substring(5, 5);
            evaluation.ENTRYDATE = evaluationDate;

            EvaluationType file = _evalRepo.GetRules(evalType, language);
            
            RulesResult rulesResult = RunRulesForFile(file, h2k, fileIdentifier, evaluationDate, eval, language);
      
            evaluation.EVALUATIONSTATUS = rulesResult.EvaluationStatus;

            try
            {
                Evaluation evalDt = rulesResult.Evaluation;
                DataTable eves = DataTableHelper.CreateEvaluationDataTable(evaluation, h2k, file.EvaluationXMLPathLookUps, out evalDt);
                
                if (rulesResult.EvaluationStatus.Equals("Draft"))
                {
                    rulesResult.UpdateEvaluationDifference = _evalRepo.GetEvaluationDifference(eves);
                }
            }
            catch (Exception e)
            {
                rulesResult.Logs.Add($"Update for File:{fileIdentifier} {e.Message}");
            }

            rulesResult.FileType = fileType;

            return rulesResult;
        }

        public async Task<RulesResult> GetUpdateAcceptedSubmissionRuleResults(EvaluationFile eval, string language)
        {
            string h2kFile = await _h2kRepo.GetH2KFileAsync(eval);

            XmlDocument h2k = new XmlDocument();
            h2k.LoadXml(h2kFile);

            //Parse initial portions of the XML for use in calling DB for Rules
            string program = XMLHelper.GetXmlTsvValue("//HouseFile/Program/Labels/English", h2k, -1);
            string version = XMLHelper.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/ProgramName", h2k, 0);
            DateTime evaluationDate = Convert.ToDateTime(XMLHelper.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/EntryDate", h2k, 0));
            string fileIdentifier = XMLHelper.GetXmlTsvValue("//HouseFile/ProgramInformation/File/Identification", h2k, -1);

            string fileType = fileIdentifier.Substring(4, 1);

            EvaluationType evalType = new EvaluationType();
            evalType.Program = program;
            evalType.FileType = fileType;
            evalType.FileVersion = version;
            evalType.SubmissionType = eval.SubmissionType;
            Evaluation evaluation = new Evaluation();

            evaluation.EVALTYPE = fileType;
            evaluation.FILENUMBER = fileIdentifier;
            evaluation.CREATIONDATE = DateTime.Now;
            evaluation.MODIFICATIONDATE = DateTime.Now;
            evaluation.BatchId = eval.EvaluationFileBatchId;
            
            evaluation.EVALUATOR = fileIdentifier.Substring(2, 2);
            evaluation.PARTNER = fileIdentifier.Substring(0, 2);
            evaluation.IDNUMBER = fileIdentifier.Substring(5, 5);


            evaluation.EVALUATIONSTATUS = "Draft";

            EvaluationType file = _evalRepo.GetRules(evalType, language);

            if (file.FileType.Equals("E") && string.IsNullOrEmpty(XMLHelper.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/PreviousFileID", h2k, 0)))
            {
                string previousFileId = evaluation.PARTNER + evaluation.EVALUATOR + "D" + evaluation.IDNUMBER;
                XmlNode myNode = h2k.SelectSingleNode("//HouseFile/Program/Results/Tsv/PreviousFileID");
                myNode.Attributes[0].InnerText = previousFileId;
            }

            Evaluation evalDT = new Evaluation();

            DataTable eves = DataTableHelper.CreateEvaluationDataTable(evaluation, h2k, file.EvaluationXMLPathLookUps, out evalDT);
            evalDT.BatchId = eval.EvaluationFileBatchId;
            eves.Rows[0].SetField("BatchId", eval.EvaluationFileBatchId);

            RulesResult rulesResult = new RulesResult();

            try
            {
                int evaluationsId = -1;
                evaluationsId = _evalRepo.SetEvaluation(eves, eval.SubmissionType);
                ERSExtention ersExt = new ERSExtention();
                evalDT.EVALUATIONSID = evaluationsId;
                if (evaluationsId != -1)
                {
                    ersExt.EVAL_ID = evaluationsId;
                    ersExt.EVAL_TYPE = fileType;
                    ersExt.FILE_NAME = eval.EvaluationFileName;
                    DataTable ers = DataTableHelper.CreateERSDataTable(ersExt, h2k, file.EvaluationXMLPathLookUps);
                    _evalRepo.SetERSExtention(ers, eval.SubmissionType);
                }
            }
            catch (Exception e)
            {
                rulesResult.Logs.Add($"Accepted Update for File:{fileIdentifier} {e.Message}");
            }

            rulesResult.Evaluation = evalDT;			
            rulesResult.FileType = fileType;
            rulesResult.EvaluationStatus = evaluation.EVALUATIONSTATUS;
            rulesResult.BatchId = eval.EvaluationFileBatchId;

            return rulesResult;
        }
        protected bool PreviousCompareEachValue(RuleDefinitionCompare defCompare, XmlDocument h2k)
        {
            bool result = true;
            string[] logic = null;
            string[] values = null;

            string fieldOne = XMLHelper.GetXmlTsvValue(defCompare.FieldName, h2k, defCompare.Attribute);

            if (!(defCompare.FieldOneAdditionalLogic is null) && !string.IsNullOrEmpty(fieldOne))
            {
                logic = defCompare.FieldOneAdditionalLogic.Split(" ");
                
                values = fieldOne.Split(logic[2]);
            }
            string[] logic2 = null;
            string prevFileId = XMLHelper.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/PreviousFileID", h2k, 0);
            string fieldTwo = _evalRepo.GetLookUpRuleResults(defCompare.RuleDefinitionId, $"FileNumber|{prevFileId}", null, null);
            string[] values2 = null;
            if (!(values is null))
            {
                values2 = new string[values.Length];


                if (!(defCompare.FieldTwoAdditionalLogic is null) && !string.IsNullOrEmpty(fieldTwo))
                {
                    logic2 = defCompare.FieldTwoAdditionalLogic.Split(" ");
                    values2 = fieldTwo.Split(logic2[2]);
                }
                else
                {
                    for (int i = 0; i < values.Length; i++)
                    {
                        values2.SetValue(fieldTwo, i);
                    }
                }
            }



            if (!(values is null))
            {
                if (logic[1].ToUpper().Equals("EVEN"))
                {
                    for (int i = 0; i < values.Length; i++)
                    {

                        if ((i + 1) % 2 == 0)
                        {
                            System.Data.DataTable table = new System.Data.DataTable();
                            string fieldValue = values[i];
                            string fieldTwoValue = values2[i];
                            decimal decValue;
                            DataRow[] dr;
                            if (decimal.TryParse(fieldTwoValue, out decValue))
                            {

                                table.Columns.Add("value", typeof(decimal));

                                System.Data.DataRow r = table.NewRow();
                                r["value"] = fieldValue;
                                table.Rows.Add(r);
                                dr = table.Select("value " + defCompare.Operator + " " + fieldTwoValue);
                            }
                            else
                            {
                                table.Columns.Add("value", typeof(string));

                                System.Data.DataRow r = table.NewRow();
                                r["value"] = fieldValue;
                                table.Rows.Add(r);
                                dr = table.Select("value " + defCompare.Operator + " '" + fieldTwoValue + "'");
                            }
                            result = result && dr.Length > 0;
                        }

                    }
                }
                else
                {


                    for (int i = 0; i < values.Length; i++)
                    {
                        if ((i + 1) % 2 != 0)
                        {
                            System.Data.DataTable table = new System.Data.DataTable();
                            string fieldValue = values[i];
                            string fieldTwoValue = values2[i];
                            decimal decValue;
                            DataRow[] dr;
                            if (decimal.TryParse(fieldTwoValue, out decValue))
                            {

                                table.Columns.Add("value", typeof(decimal));

                                System.Data.DataRow r = table.NewRow();
                                r["value"] = fieldValue;
                                table.Rows.Add(r);
                                dr = table.Select("value " + defCompare.Operator + " " + fieldTwoValue);
                            }
                            else
                            {
                                table.Columns.Add("value", typeof(string));

                                System.Data.DataRow r = table.NewRow();
                                r["value"] = fieldValue;
                                table.Rows.Add(r);
                                dr = table.Select("value " + defCompare.Operator + " '" + fieldTwoValue + "'");
                            }
                            result = result && dr.Length > 0;
                        }
                    }
                }
            }
            return result;
        }
        protected async Task<string> CurrentValueLookUp(int RuleDefinitionId, XmlDocument h2k, string AdditionalLogic, string Field, int attribute)
        {
            string result = null;
            string fileId = XMLHelper.GetXmlTsvValue("//HouseFile/ProgramInformation/File/Identification", h2k, -1);
            if (!string.IsNullOrEmpty(fileId))
            {
                if (AdditionalLogic.Split(" ")[2].ToUpper().Equals("TABLE"))
                {
                    result = _evalRepo.GetLookUpRuleResults(RuleDefinitionId, $"FileNumber|{fileId}", null, null);
                }
                else if (AdditionalLogic.Split(" ")[2].ToUpper().Equals("FILE"))
                {
                    try
                    {
                        EvaluationFileBatch fileBatch = _evalRepo.GetEvaluationFileBatch(fileId);

                        string h2kFile = await _h2kRepo.GetH2KFileBatchAsync(fileBatch);

                        XmlDocument h2kXml = new XmlDocument();
                        h2kXml.LoadXml(h2kFile);

                        result = XMLHelper.GetXmlTsvValue(Field, h2kXml, attribute);
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e.Message);
                    }
                }
            }
            return result;
        }
        protected async Task<string> PreviousValueLookUp(int RuleDefinitionId, XmlDocument h2k, string AdditionalLogic, string Field, int attribute)
        {
            string result = null;
            string prevFileId = XMLHelper.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/PreviousFileID", h2k, 0);
            if (!string.IsNullOrEmpty(prevFileId))
            {
                if (AdditionalLogic.Split(" ")[2].ToUpper().Equals("TABLE"))
                {
                    // string prevFileId = XMLHelper.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/PreviousFileID", h2k, 0);
                    result = _evalRepo.GetLookUpRuleResults(RuleDefinitionId, $"FileNumber|{prevFileId}", null, null);
                }
                else if (AdditionalLogic.Split(" ")[2].ToUpper().Equals("FILE"))
                {
                    try
                    {
                        EvaluationFileBatch fileBatch = _evalRepo.GetEvaluationFileBatch(prevFileId);

                        string h2kFile = await _h2kRepo.GetH2KFileBatchAsync(fileBatch);

                        XmlDocument h2kXml = new XmlDocument();
                        h2kXml.LoadXml(h2kFile);

                        result = XMLHelper.GetXmlTsvValue(Field, h2kXml, attribute);
                    } catch (Exception e)
                    {
                        Console.WriteLine(e.Message);
                    }
                }
            }
            return result;
        }
        protected string CountTags(string Field, XmlDocument h2k)
        {
            string result = "0";

            XmlNode ruleNode = h2k.SelectSingleNode(Field);
            if (ruleNode is null)
            {
                result = "0";
            }
            else
            {
                result = ruleNode.SelectNodes("*").Count.ToString();
            }

            return result;
        }

        protected bool AshpModelCheck(XmlDocument h2k)
        {
            bool result = true;

            string furnace = XMLHelper.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/FurnaceModel", h2k, 0);
            if (furnace.Contains(";") && furnace.Length > 6)
            {
                result = true;
            }
            else
            {
                result = false;
            }

            return result;
        }

        protected bool FanModelCheck(XmlDocument h2k)
        {
            bool result = true;

            string ventexh = XMLHelper.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/TotalVentExh", h2k, 0);
            string ventsup = XMLHelper.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/TotalVentSupply", h2k, 0);
            if ((Convert.ToDecimal(ventexh) - Convert.ToDecimal(ventsup)) <= 42)
            {
                result = true;
            }
            else
            {
                result = false;
            }

            return result;
        }
        protected bool CeilingInsulationCheck(XmlDocument h2k)
        {
            bool result = true;

            XmlNodeList xnList = h2k.SelectNodes("//HouseFile/House/Components/Ceiling/Construction/CeilingType[@nominalInsulation ='0.0']");
            if (xnList.Count == 0)
            {
                result = true;
            }
            else
            {
                result = false;
            }

            return result;
        }
        protected bool BasementPerimeterCheck(XmlDocument h2k)
        {
            bool result = true;
            string fndType = XMLHelper.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/FNDTYPE", h2k, 0);
            string houseType = XMLHelper.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/TypeOfHouse", h2k, 0);
            List<string> typeOfHouse = new List<string>(){ "Double / Semi Detached"
                                    , "Row house end unit"
                                    , "row house middle unit"
                                    , "Attached duplex"
                                    , "attached triplex"
                                    ,"apartment row" };
            if (fndType.Contains("B2") || fndType.Contains("C1") || typeOfHouse.Contains(houseType))
            {
                decimal? BasementExposedSurfacePermineter = Convert.ToDecimal(XMLHelper.GetXmlTsvValue("//HouseFile/House/Components/Basement", h2k, 1));
                decimal? CrawlspaceExposedSurfacePermineter = Convert.ToDecimal(XMLHelper.GetXmlTsvValue("//HouseFile/House/Components/Crawlspace", h2k, 1));
                decimal? SlabExposedSurfacePermineter = Convert.ToDecimal(XMLHelper.GetXmlTsvValue("//HouseFile/House/Components/Slab", h2k, 1));
                if (!(BasementExposedSurfacePermineter is null))
                {
                    if (!(Convert.ToDecimal(XMLHelper.GetXmlTsvValue("//HouseFile/House/Components/Basement/Floor/Measurements", h2k, 2)) != 0))
                    {
                        if (BasementExposedSurfacePermineter < Convert.ToDecimal(XMLHelper.GetXmlTsvValue("//HouseFile/House/Components/Basement/Floor/Measurements", h2k, 2)))
                        {
                            return false;
                        }
                        if (!(Convert.ToDecimal(XMLHelper.GetXmlTsvValue("//HouseFile/House/Components/Basement/Floor/Measurements", h2k, 3)) != 0))
                        {
                            if (BasementExposedSurfacePermineter <
                                ((Convert.ToDecimal(XMLHelper.GetXmlTsvValue("//HouseFile/House/Components/Basement/Floor/Measurements", h2k, 3)) * 2) + (Convert.ToDecimal(XMLHelper.GetXmlTsvValue("//HouseFile/House/Components/Basement/Floor/Measurements", h2k, 4)) * 2)))
                            {
                                return false;
                            }

                        }
                    }
                }
                if (!(CrawlspaceExposedSurfacePermineter is null))
                {
                    if (!(Convert.ToDecimal(XMLHelper.GetXmlTsvValue("//HouseFile/House/Components/Crawlspace/Floor/Measurements", h2k, 2)) != 0))
                    {
                        if (CrawlspaceExposedSurfacePermineter < Convert.ToDecimal(XMLHelper.GetXmlTsvValue("//HouseFile/House/Components/Crawlspace/Floor/Measurements", h2k, 2)))
                        {
                            return false;
                        }
                        if (!(Convert.ToDecimal(XMLHelper.GetXmlTsvValue("//HouseFile/House/Components/Crawlspace/Floor/Measurements", h2k, 3)) != 0))
                        {
                            if (CrawlspaceExposedSurfacePermineter < ((Convert.ToDecimal(XMLHelper.GetXmlTsvValue("//HouseFile/House/Components/Crawlspace/Floor/Measurements", h2k, 3)) * 2) + (Convert.ToDecimal(XMLHelper.GetXmlTsvValue("//HouseFile/House/Components/Crawlspace/Floor/Measurements", h2k, 4)) * 2)))
                            {
                                return false;
                            }

                        }
                    }
                }
                if (!(SlabExposedSurfacePermineter is null))
                {
                    if (!(Convert.ToDecimal(XMLHelper.GetXmlTsvValue("//HouseFile/House/Components/Slab/Floor/Measurements", h2k, 2)) != 0))
                    {
                        if (SlabExposedSurfacePermineter < Convert.ToDecimal(XMLHelper.GetXmlTsvValue("//HouseFile/House/Components/Slab/Floor/Measurements", h2k, 2)))
                        {
                            return false;
                        }
                        if (!(Convert.ToDecimal(XMLHelper.GetXmlTsvValue("//HouseFile/House/Components/Slab/Floor/Measurements", h2k, 3)) != 0))
                        {
                            if (SlabExposedSurfacePermineter < (Convert.ToDecimal(XMLHelper.GetXmlTsvValue("//HouseFile/House/Components/Slab/Floor/Measurements", h2k, 3)) * 2) + ((Convert.ToDecimal(XMLHelper.GetXmlTsvValue("//HouseFile/House/Components/Slab/Floor/Measurements", h2k, 4)) * 2)))
                            {
                                return false;
                            }

                        }
                    }
                }
            }

            return result;
        }
        protected bool CeilingAreaCheck(XmlDocument h2k)
        {
            bool result = true;
            XmlNodeList xnList = h2k.SelectNodes("//HouseFile/House/Components/Ceiling/Measurements");
            decimal ceilingArea = 0M;
            foreach (XmlNode xn in xnList)
            {
                ceilingArea += Convert.ToDecimal(xn.Attributes[1].InnerText);
            }
            xnList = h2k.SelectNodes("//HouseFile/House/Components/Floor/Measurements");
            decimal floorArea = 0M;
            foreach (XmlNode xn in xnList)
            {
                floorArea += Convert.ToDecimal(xn.Attributes[0].InnerText);
            }
            ceilingArea = Decimal.Add(ceilingArea, 1.00M);
            if ( ceilingArea < (floorArea
                    + Convert.ToDecimal(XMLHelper.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/BasementFloorAr", h2k, 0))
                    + Convert.ToDecimal(XMLHelper.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/CrawlSpFloorAr", h2k, 0))
                    + Convert.ToDecimal(XMLHelper.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/SlabFloorArea", h2k, 0))))
            {
                result = false;
            }
            return result;
        }

        protected async Task<bool> WindowCheck(XmlDocument h2k)
        {
            bool result = true;
            XmlNodeList xnList = h2k.SelectNodes("//HouseFile/House/Components/Wall/Components/Window/Measurements");

            string prevFileId = XMLHelper.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/PreviousFileID", h2k, 0);
            EvaluationFileBatch fileBatch = _evalRepo.GetEvaluationFileBatch(prevFileId);

            string h2kFile = await _h2kRepo.GetH2KFileBatchAsync(fileBatch);

            XmlDocument prevH2k = new XmlDocument();
            prevH2k.LoadXml(h2kFile);

            XmlNodeList xnPrevList = prevH2k.SelectNodes("//HouseFile/House/Components/Wall/Components/Window/Measurements");
            List<bool> checks = new List<bool>();
            foreach (XmlNode xn in xnList)
            {
                string height = xn.Attributes[0].InnerText;
                string width = xn.Attributes[1].InnerText;
                XmlNodeList xmlNodesPrev = prevH2k.SelectNodes($"//HouseFile/House/Components/Wall/Components/Window/Measurements[@height={height} and @width={width}]");
                XmlNodeList xmlNodes = h2k.SelectNodes($"//HouseFile/House/Components/Wall/Components/Window/Measurements[@height={height} and @width={width}]");

                if (xmlNodes.Count == xmlNodesPrev.Count)
                {
                    checks.Add(true);
                }
                else
                {
                    checks.Add(false);
                }

            }

            if (!(checks.Contains(false)))
            {
                result = false;
            }
            return result;
        }

        protected string Math(string fieldValue, string additionalLogic, XmlDocument h2k)
        {
            decimal decValue = 0M;
            string[] logic = null;
            additionalLogic = additionalLogic.Substring(5);

            logic = additionalLogic.Split(" ");
            string expression = fieldValue;
            DataTable dt = new DataTable();


            foreach (var item in logic)
            {
                if (item.Length > 3 && item.Substring(0, 3) == "XML")
                    expression = expression + XMLHelper.GetXmlTsvValue(item.Substring(3), h2k, 0);
                else
                    expression = expression + item;
            }

            decValue = Convert.ToDecimal(dt.Compute(expression, string.Empty));
            return decValue.ToString();
        }
        protected static List<string> FindStringOccurrences(string text, string pattern)
        {
            // Loop through all instances of the string 'text'.

            List<string> textToReplace = new List<string>();
            int i = 0;
            while ((i = text.IndexOf(pattern, i)) != -1)
            {
                int len = text.IndexOf(">", i);
                textToReplace.Add(text.Substring(i, len - (i - 1)));
                i = text.IndexOf(">", i) + 1;
            }
            return textToReplace;
        }
        protected string LookUp(XmlDocument h2k, RuleDefinition rule, int ruleDefinitionId, string fileNumber, string previousFileId)
        {
            string result = null;
            //if (!(string.IsNullOrEmpty(XMLHelper.GetXmlTsvValue(rule.FieldName, h2k, rule.Attribute))))
            //{
                string wherefilter = null;
                string mvcharact = "~";
                string svcharact = "|";
                List<RuleDefinitionLookUp> ruleDefinitionLookUps = new List<RuleDefinitionLookUp>();
                if (rule.RuleType.Equals("Compare"))
                {
                    ruleDefinitionLookUps = rule.RuleDefinitionCompares.Find(x => x.RuleDefinitionId == ruleDefinitionId).CompareRuleDefinitionLookUps;
                }
                else
                {
                    ruleDefinitionLookUps = rule.RuleDefinitionLookUps;
                }

                foreach (RuleDefinitionLookUp lookup in ruleDefinitionLookUps)
                {
                    string lookupvalue = XMLHelper.GetXmlTsvValue(lookup.FieldName, h2k, lookup.Attribute);
                    var substring = lookup.SubStringLogic;
                    var splitValue = lookup.SplitValue;
                    string[] lookUpValues;
                    var splitNumber = Convert.ToInt32(lookup.SplitNumber);
                    string lookupField = lookup.LookUpField;
                    if (splitValue != null && splitNumber == -1)
                    {
                        lookUpValues = lookupvalue.Split(splitValue.ToString());

                        foreach (string lookupVal in lookUpValues)
                        {
                            lookupvalue = lookupVal;
                            if (substring != null)
                            {
                                int value1 = Convert.ToInt32(substring.ToString().Split(',')[0]);
                                int value2 = Convert.ToInt32(substring.ToString().Split(',')[1]);
								// Only run the substring function against the lookupvalue if it has enough characters
								if (lookupvalue.Length >= (value1 + value2))
								{
									lookupvalue = lookupvalue.Substring(value1, value2);
								}
                            }
                            lookupvalue = string.IsNullOrEmpty(lookup.OverRideValue) ? lookupvalue + lookup.AdditionalLogic ?? "" : lookup.OverRideValue;
                            if (wherefilter is null)
                            {
                                wherefilter = lookupField + svcharact + lookupvalue;
                            }
                            else
                            {
                                wherefilter += mvcharact + lookupField + svcharact + lookupvalue;
                            }                                                        
                        }
                    }
                    else if (!String.IsNullOrEmpty(lookup.AdditionalLogic) && lookup.AdditionalLogic.StartsWith("FORMAT:"))
                    {
                        lookupvalue = lookupvalue.ToString().Trim();
                        if (lookupvalue.Trim().Length == 6)
                        {
                            string len1 = lookupvalue.Substring(0, System.Math.Min(3, lookupvalue.Length));
                            string len2 = lookupvalue.Substring(System.Math.Min(3, lookupvalue.Length));
                            lookupvalue = $"{len1} {len2}";
                        }
                        if (wherefilter is null)
                        {
                            wherefilter = lookupField + svcharact + lookupvalue;
                        }
                        else
                        {
                            wherefilter += mvcharact + lookupField + svcharact + lookupvalue;
                        }
                    }
                    else
                    {
                        if (splitValue != null)
                        {
                            lookupvalue = lookupvalue.Split(splitValue.ToString())[Convert.ToInt32(splitNumber)];
                        }
                        if (substring != null)
                        {
                            int value1 = Convert.ToInt32(substring.ToString().Split(',')[0]);
                            int value2 = Convert.ToInt32(substring.ToString().Split(',')[1]);
							// Only run the substring function against the lookupvalue if it has enough characters
							if (lookupvalue.Length >= (value1 + value2))
							{
								lookupvalue = lookupvalue.Substring(value1, value2);
							}
                        }
                        lookupvalue = lookup.OverRideValue is null ? lookupvalue + lookup.AdditionalLogic ?? "" : lookup.OverRideValue;
                        if (wherefilter is null)
                        {
                            wherefilter = lookupField + svcharact + lookupvalue;
                        }
                        else
                        {
                            wherefilter += mvcharact + lookupField + svcharact + lookupvalue;
                        }
                        
                    }
                }

                result = _evalRepo.GetLookUpRuleResults(ruleDefinitionId, wherefilter, fileNumber, previousFileId);

            //}
            return result;
        }
        protected static bool CheckIfAny(RuleDefinitionCompare defCompare, XmlDocument h2k)
        {
            var test = h2k.SelectNodes(defCompare.FieldName);
            string fieldTwoValue = defCompare.FieldTwoValue;
            if (test == null)
            {
                return true;
            }
            if (defCompare.FieldTwoType.Equals("XML"))
            {
                fieldTwoValue = XMLHelper.GetXmlTsvValue(defCompare.FieldTwoValue, h2k, defCompare.Attribute);
            }
            foreach (XmlNode n in test)
            {
                string fieldValue = defCompare.Attribute != -1 ? n.Attributes[defCompare.Attribute].Value : n.InnerText;
                System.Data.DataTable table = new System.Data.DataTable();
                decimal decValue2;
                decimal decValue;
                DataRow[] dr;
                if (decimal.TryParse(fieldTwoValue, out decValue) && decimal.TryParse(fieldValue, out decValue2))
                {

                    table.Columns.Add("value", typeof(decimal));

                    System.Data.DataRow r = table.NewRow();
                    r["value"] = decValue2;
                    table.Rows.Add(r);
                    dr = table.Select("value " + defCompare.Operator + " " + fieldTwoValue  + "");
                }
                else
                {
                    table.Columns.Add("value", typeof(string));

                    System.Data.DataRow r = table.NewRow();
                    r["value"] = fieldValue;
                    table.Rows.Add(r);
                    string a = "value " + defCompare.Operator + " '" + fieldTwoValue + "'";
                    dr = table.Select("value " + defCompare.Operator + " '" + fieldTwoValue + "'");
                }
                if (dr.Length > 0)
                {
                    return false;
                    
                }

            }
            return true;

        }

        protected static string SumSet(RuleDefinitionCompare defCompare, XmlDocument h2k, string fieldPosition)
        {
            List<string> logic;
            List<string> fieldValues;

            // fieldValues = "89.1;10.0;10.9;10.0;100;10.0"
            // logic = "SumSet EVEN ; //HouseFile/Program/Results/Tsv/FNDTYPE F,B"

            switch (fieldPosition)
            {
                case "FieldOne":
                    logic = defCompare.FieldOneAdditionalLogic.Split(" ").ToList();
                    fieldValues = XMLHelper.GetXmlTsvValue(defCompare.FieldName, h2k, defCompare.Attribute).Split(logic[2]).ToList();

                    break;
                case "FieldTwo":
                    logic = defCompare.FieldTwoAdditionalLogic.Split(" ").ToList();

                    if (defCompare.FieldTwoType.ToUpper().Equals("XML"))
                    {
                        fieldValues = XMLHelper.GetXmlTsvValue(defCompare.FieldTwoValue, h2k, defCompare.Attribute).Split(logic[2]).ToList();
                    }
                    else
                    {
                        fieldValues = defCompare.FieldTwoValue.Split(logic[2]).ToList();
                    }

                    break;
                default:
                    return "";
            }

            var types = logic[4].Split(",").ToList();   // F,B
            var typeValues = XMLHelper.GetXmlTsvValue(logic[3], h2k, defCompare.Attribute).Split(logic[2]).ToList(); // B1;C1;F1

            decimal? sumResult = null;
            switch (logic[1].ToUpper())
            {
                case "EVEN":
                    for (int i = 0; i < fieldValues.Count; i++)
                    {
                        if ((i + 1) % 2 == 0 && types.Contains(typeValues[(i - 1) / 2].Substring(0, 1)))
                        {
                            sumResult ??= 0M;
                            sumResult += Convert.ToDecimal(fieldValues[i]);
                        }
                    }

                    break;
                case "ODD":
                    for (int i = 0; i < fieldValues.Count; i++)
                    {
                        if (i % 2 == 0 && types.Contains(typeValues[(i / 2)].Substring(0, 1)))
                        {
                            sumResult ??= 0M;
                            sumResult += Convert.ToDecimal(fieldValues[i]);
                        }
                    }

                    break;
                default:
                    return "";
            }
            //returning fieldtwo value if there isn't any sets to sum in the value.
            //That way not matter if we have sumset on AddTwologic as well and it should be a consistent set to sum and fieldtwo=fieldtwo
            return sumResult == null ? defCompare.FieldTwoValue : sumResult.ToString();
        }
    }
}
