﻿using System;
using System.IO;
using EvaluationValidationEcosystemFunction.Domain.Response;
using EvaluationValidationEcosystemFunction.Domain.Request;
using System.Threading.Tasks;

namespace EvaluationValidationEcosystemFunction.Tasks
{
    public interface IRunRules
    {        
        Task<RulesResult> GetInitialSubmissionRuleResults(EvaluationFile eval, string language);
        Task<RulesResult> GetUpdateSubmissionRuleResults(EvaluationFile eval, string language);
        Task<RulesResult> GetUpdateAcceptedSubmissionRuleResults(EvaluationFile eval, string language);
    }
}
