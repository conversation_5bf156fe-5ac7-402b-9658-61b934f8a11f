
using EvaluationValidationEcosystemFunction.Data.Repository.Evaluation;
using EvaluationValidationEcosystemFunction.Data.ServiceBus;
using EvaluationValidationEcosystemFunction.Domain.Evaluation;
using EvaluationValidationEcosystemFunction.Domain.Response;
using EvaluationValidationEcosystemFunction.Domain.ServiceBusMessage;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace EvaluationValidationEcosystemFunction
{
    public class EvaluationFunction
    {
        private readonly IEvaluationRepository _repository;
        private readonly IServiceBusQueue<EvaluationReprocessEvent> _serviceBusQueue;
        public EvaluationFunction(IEvaluationRepository repository, IServiceBusQueue<EvaluationReprocessEvent> serviceBusQueue)
        {
            _repository = repository;
            _serviceBusQueue = serviceBusQueue;
        }

        [FunctionName("EvaluationStatus")]
        public async Task<IActionResult> EvaluationStatus([HttpTrigger(AuthorizationLevel.Anonymous, "patch", Route = "EvaluationStatus")] HttpRequest req, ILogger log)
        {
            log.LogInformation($"Entering into Evaluation.{nameof(EvaluationStatus)}");
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var data = JsonConvert.DeserializeObject<EvalutionStatus>(requestBody);
            DataTable dat = JsonConvert.DeserializeObject<DataTable>(JsonConvert.SerializeObject(data.FileNumbers));
            var result = _repository.SetEvaluationStatus(dat, data.EvaluationStatus);
            List < ServiceBusEvent<EvaluationReprocessEvent>> events = new List<ServiceBusEvent<EvaluationReprocessEvent>>();
            foreach (var item in result.Data.Where(x => !string.IsNullOrEmpty(x.FileToReprocess)).ToList())
            {
                events.Add(new ServiceBusEvent<EvaluationReprocessEvent>(){ EventType = "EvaluationReprocess",EventInformation = new EvaluationReprocessEvent() { FileNumber = item.FileToReprocess } });
            }

            var sbResult =  _serviceBusQueue.Send(events);

            return new OkObjectResult(result);
        }        
    }   
}
