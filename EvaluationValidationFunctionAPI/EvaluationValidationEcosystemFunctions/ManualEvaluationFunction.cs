using EvaluationValidationEcosystemFunction.Data.Repository.ManualEvaluation;
using EvaluationValidationEcosystemFunction.Domain.ManualEvaluation;
using EvaluationValidationEcosystemFunction.Domain.Response;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.IO;
using System.Threading.Tasks;
namespace EvaluationValidationEcosystemFunction
{
    public class ManualEvaluationFunction
    {
        private readonly IManualEvaluationRepository _repository;
        public ManualEvaluationFunction(IManualEvaluationRepository repository)
        {
            _repository = repository;
        }
        [FunctionName("ManualEvaluationRequest")]
        public async Task<IActionResult> ManualEvaluationRequest(
            [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = null)] HttpRequest req,
            ILogger log)
        {

            log.LogInformation($"Entering into ManualEvaluationFunction.{nameof(ManualEvaluationRequest)}");
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            EvaluationRequest input = JsonConvert.DeserializeObject<EvaluationRequest>(requestBody);
            input.UserId = req.Headers["UserId"].ToString();
            input.ServiceOrganization = req.Headers["Org"].ToString();
            var success = _repository.SetManualEvaluationRequest(input);
            return new OkObjectResult(new ServiceResponse<string>() { Data = input.FileNumber, Success = success > 0 });
        }
        [FunctionName("ManualEvaluationSearchRequest")]
        public async Task<IActionResult> ManualEvaluationSearchRequest(
           [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = null)] HttpRequest req,
           ILogger log)
        {
            log.LogInformation($"Entering into ManualEvaluationSearchRequest.{nameof(ManualEvaluationSearchRequest)}");
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            ManualEvaluationSearchParameter input = JsonConvert.DeserializeObject<ManualEvaluationSearchParameter>(requestBody);
            return new OkObjectResult(_repository.SearchManualEvaluationRequests(input));
        }
        [FunctionName("GetManualEvaluationRequest")]
        public IActionResult GetManualEvaluationRequest(
        [HttpTrigger(AuthorizationLevel.Anonymous, "Get", Route = "GetManualEvaluationRequest/{ID}")] HttpRequest req,
        ILogger log, long id)
        {
            log.LogInformation($"Entering into ManualEvaluationSearchRequest.{nameof(GetManualEvaluationRequest)}");
            return new OkObjectResult(_repository.SearchManualEvaluationById(id));
        }
        [FunctionName("ManualEvaluationRequestAssign")]
        public IActionResult ManualEvaluationRequestAssign(
          [HttpTrigger(AuthorizationLevel.Anonymous, "put", Route = null)] HttpRequest req,
          ILogger log)
        {
            log.LogInformation($"Entering into ManualEvaluationRequestAssign.{nameof(ManualEvaluationRequestAssign)}");
            var result = _repository.SetManualEvaluationRequestAssignTo(Convert.ToInt64(req.Query["Id"].ToString()), req.Headers["UserId"].ToString());
            return new OkObjectResult(new ServiceResponse<bool>() { Data = result, Success = result });
        }
    }
}
