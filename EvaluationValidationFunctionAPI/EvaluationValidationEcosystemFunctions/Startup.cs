﻿//using EvaluationValidationEcosystemFunction.Data;
//using Microsoft.Azure.Functions.Extensions.DependencyInjection;
//using Microsoft.Extensions.DependencyInjection;
//using System;
//[assembly: FunctionsStartup(typeof(EvaluationValidationEcosystemFunction.Startup))]

//namespace EvaluationValidationEcosystemFunction
//{
//    public class Startup : FunctionsStartup
//    {
//        public override void Configure(IFunctionsHostBuilder builder)
//        {
//            builder.Services.Add(config =>
//            {
//                config.UseSqlServer(Environment.GetEnvironmentVariable("ApplicationDB"));

//                AddDbContext(config =>
//            {
//                config.UseSqlServer(Environment.GetEnvironmentVariable("ApplicationDB"));
//            });

//                builder.Services.AddScoped<IRepositoryWrapper, RepositoryWrapper>();
//            }
//    }
//    }
using EvaluationValidationEcosystemFunction.Core;
using EvaluationValidationEcosystemFunction.Data.Repository;
using EvaluationValidationEcosystemFunction.Data.ServiceBus;
using EvaluationValidationEcosystemFunction.Data.Repository.H2KFile;
using EvaluationValidationEcosystemFunction.Data.Repository.ManualEvaluation;
using EvaluationValidationEcosystemFunction.Data.Repository.Rule;
using EvaluationValidationEcosystemFunction.Data.Repository.Evaluation;
using EvaluationValidationEcosystemFunction.Domain.Response;
using EvaluationValidationEcosystemFunction.Tasks;
using Microsoft.Azure.Functions.Extensions.DependencyInjection;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.IO;
using Microsoft.Extensions.Azure;
using System;
using EvaluationValidationEcosystemFunction.Domain.ServiceBusMessage;

[assembly: FunctionsStartup(typeof(EvaluationValidationEcosystemFunction.Startup))]

namespace EvaluationValidationEcosystemFunction
{
    public class Startup : FunctionsStartup
    {
        public override void ConfigureAppConfiguration(IFunctionsConfigurationBuilder builder)
        {
            SqlAuthenticationProvider.SetProvider(SqlAuthenticationMethod.ActiveDirectoryInteractive, new ADAuthenticationProvider());
            FunctionsHostBuilderContext context = builder.GetContext();

            // Note that these files are not automatically copied on build or publish. 
            // See the csproj file to for the correct setup.
            builder.ConfigurationBuilder
                .AddJsonFile(Path.Combine(context.ApplicationRootPath, "local.settings.json"), optional: true, reloadOnChange: false)
                .AddJsonFile(Path.Combine(context.ApplicationRootPath, $"local.settings.{context.EnvironmentName}.json"), optional: true, reloadOnChange: false);



        }

        public override void Configure(IFunctionsHostBuilder builder)
        {
            builder.Services.AddAzureClients(builder =>
            {
                builder.AddServiceBusClient(Environment.GetEnvironmentVariable("ServiceBusSAS"))
                          // (Optional) Provide name for instance to retrieve by with DI
                          .WithName("ServiceBusQueue")
                          // (Optional) Override ServiceBusClientOptions (e.g. change retry settings)
                          .ConfigureOptions(options =>
                          {
                              options.RetryOptions.Delay = TimeSpan.FromMilliseconds(50);
                              options.RetryOptions.MaxDelay = TimeSpan.FromSeconds(5);
                              options.RetryOptions.MaxRetries = 3;
                          });
            });
            builder.Services.AddScoped<IServiceBusQueue<EvaluationReprocessEvent>, ServiceBusQueue<EvaluationReprocessEvent>>();
            builder.Services.AddScoped<DbContext>();
            builder.Services.AddScoped<ADLDbContext>();
            builder.Services.AddScoped<IRunRules, RunRules>();
            builder.Services.AddScoped<IH2KFileRepository, H2KFileRepository>();
            builder.Services.AddScoped<IEvaluationValidationRepository, EvaluationValidationRepository>();
            builder.Services.AddScoped<IEvaluationRepository, EvaluationRepository>();
            builder.Services.AddScoped<IManualEvaluationRepository, ManualEvaluationRepository>();
            FunctionsHostBuilderContext context = builder.GetContext();
            builder.Services.Configure<RulesResult>(context.Configuration);
        }
    }
}