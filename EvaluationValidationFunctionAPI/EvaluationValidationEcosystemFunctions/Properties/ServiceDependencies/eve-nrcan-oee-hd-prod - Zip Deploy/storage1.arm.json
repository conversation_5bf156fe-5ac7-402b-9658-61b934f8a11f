{"$schema": "https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"resourceGroupName": {"type": "string", "defaultValue": "nrcan-prod-oee-hd-shared-ca-central", "metadata": {"_parameterType": "resourceGroup", "description": "Name of the resource group for the resource. It is recommended to put resources under same resource group for better tracking."}}, "resourceGroupLocation": {"type": "string", "defaultValue": "canadacentral", "metadata": {"_parameterType": "location", "description": "Location of the resource group. Resource groups could have different location than resources."}}, "resourceLocation": {"type": "string", "defaultValue": "[parameters('resourceGroupLocation')]", "metadata": {"_parameterType": "location", "description": "Location of the resource. By default use resource group's location, unless the resource provider is not supported there."}}}, "resources": [{"type": "Microsoft.Resources/resourceGroups", "name": "[parameters('resourceGroupName')]", "location": "[parameters('resourceGroupLocation')]", "apiVersion": "2019-10-01"}, {"type": "Microsoft.Resources/deployments", "name": "[concat(parameters('resourceGroupName'), 'Deployment', uniqueString(concat('storageaccountnrcan8c12', subscription().subscriptionId)))]", "resourceGroup": "[parameters('resourceGroupName')]", "apiVersion": "2019-10-01", "dependsOn": ["[parameters('resourceGroupName')]"], "properties": {"mode": "Incremental", "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "resources": [{"sku": {"name": "Standard_LRS", "tier": "Standard"}, "kind": "Storage", "name": "storageaccountnrcan8c12", "type": "Microsoft.Storage/storageAccounts", "location": "[parameters('resourceLocation')]", "apiVersion": "2017-10-01"}]}}}], "metadata": {"_dependencyType": "storage.azure"}}