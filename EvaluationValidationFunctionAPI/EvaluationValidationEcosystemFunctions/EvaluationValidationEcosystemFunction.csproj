﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <AzureFunctionsVersion>v4</AzureFunctionsVersion>
    <UserSecretsId>345fcc7d-a258-46c6-9c65-810242b91866</UserSecretsId>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.Azure.Functions.Extensions" Version="1.1.0" />
    <PackageReference Include="Microsoft.Azure.Services.AppAuthentication" Version="1.6.2" />
    <PackageReference Include="Microsoft.Azure.WebJobs" Version="3.0.33" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.Storage" Version="4.0.5" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.0.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" Version="3.1.2" />
    <PackageReference Include="Microsoft.NET.Sdk.Functions" Version="4.1.3" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.5" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\EvaluationValidationEcosystemFunction.Domain\EvaluationValidationEcosystemFunction.Domain.csproj" />
    <ProjectReference Include="..\EvaluationValidationEcosystemFunction.Tasks\EvaluationValidationEcosystemFunction.Tasks.csproj" />
  </ItemGroup>
  <ItemGroup> 
    <None Update="host.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>
