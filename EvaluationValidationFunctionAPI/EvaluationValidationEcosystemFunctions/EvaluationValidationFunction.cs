
using Microsoft.Azure.WebJobs;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Threading.Tasks;
using Microsoft.Azure.WebJobs.Extensions.Http;
using EvaluationValidationEcosystemFunction.Domain.Request;
using EvaluationValidationEcosystemFunction.Tasks;
using EvaluationValidationEcosystemFunction.Domain.Response;
using System.IO;
using Microsoft.Extensions.Primitives;

namespace EvaluationValidationEcosystemFunction
{
    public class EvaluationValidationEcosystemFunction
    {        
        private readonly IRunRules _ruleRun;
        
        public EvaluationValidationEcosystemFunction(IRunRules rules)
        {
            this._ruleRun = rules;
        }

        [FunctionName("EVEInitialSubmission")]
        public async Task<IActionResult> EVEInitialSubmission([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "EVEInitialSubmission")] HttpRequest req, ILogger log)
        {
            log.LogInformation($"File Validation Started\n");
            //string requestBody = //"{\"ADLLandingContainer\": \"h2kfilelanding\",\"ADLLandingDirectory\": \"/98JHD00001/103/\",\"EvaluationFileBatchId\": 103,\"EvaluationFileName\":\"98JHD00001.h2k\",\"SubmissionType\":\"I\"}";                        
            //"{\"ADLLandingContainerName\": \"h2kfilelanding\",\"ADLLandingDirectoryName\": \"/\",\"EvaluationFileBatchId\": 237,\"EvaluationFileName\": \"98AKD01001.h2k\",  \"SubmissionType\": \"I\"}";
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            EvaluationFile input = JsonConvert.DeserializeObject<EvaluationFile>(requestBody);
           
            ServiceResponse<RulesResult> response = new ServiceResponse<RulesResult>();
            response.Data  = await _ruleRun.GetInitialSubmissionRuleResults(input, req.Headers["Language"].ToString());
            response.Success = true;
            response.Data.EvaluationFileBatchId = input.EvaluationFileBatchId;
            if (response.Data.Logs.Count > 0)
            {
                foreach (string resultLog in response.Data.Logs)
                {
                    log.LogInformation(resultLog);
                }
                response.Success = false;                
            }
            return new OkObjectResult(response);
        }

        [FunctionName("EVEUpdateSubmission")]
        public async Task<IActionResult> EVEUpdateSubmission([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "EVEUpdateSubmission")] HttpRequest req, ILogger log)
        {
            log.LogInformation($"File Validation Started\n");
            //string requestBody = "{\"ADLLandingContainerName\": \"h2kfilelanding\",\"ADLLandingDirectoryName\": \"/\",\"EvaluationFileBatchId\": 91,\"EvaluationFileName\": \"98AKD01001.h2k\",\"SubmissionType\": \"U\"}";
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            EvaluationFile input = JsonConvert.DeserializeObject<EvaluationFile>(requestBody);
            
            ServiceResponse<RulesResult> response = new ServiceResponse<RulesResult>();
            response.Data = await _ruleRun.GetUpdateSubmissionRuleResults(input, req.Headers["Language"].ToString());
            response.Success = true;
            response.Data.EvaluationFileBatchId = input.EvaluationFileBatchId;
            if (response.Data.Logs.Count > 0)
            {
                foreach (string resultLog in response.Data.Logs)
                {
                    log.LogInformation(resultLog);
                }
                response.Success = false;               
            }
            return new OkObjectResult(response);
        }

        [FunctionName("EVEUpdateAcceptedSubmission")]
        public async Task<IActionResult> EVEUpdateAcceptedSubmission([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = null)] HttpRequest req, ILogger log)
        {
            log.LogInformation($"File Validation Started\n");
            //string requestBody = "{\"ADLLandingContainer\": \"h2kfilelanding\",\"ADLLandingDirectory\": \"/\",\"EvaluationFileBatchId\": 35,\"EvaluationFileName\": \"98SSD69240.h2k\",\"SubmissionType\": \"I\"}";
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            EvaluationFile input = JsonConvert.DeserializeObject<EvaluationFile>(requestBody);
            string language = req.Headers["Language"].ToString() ?? "en";
            ServiceResponse<RulesResult> response = new ServiceResponse<RulesResult>();
            response.Data = await _ruleRun.GetUpdateAcceptedSubmissionRuleResults(input, language);
            response.Success = true;
            response.Data.EvaluationFileBatchId = input.EvaluationFileBatchId;
            if (response.Data.Logs.Count > 0)
            {
                foreach (string resultLog in response.Data.Logs)
                {
                    log.LogInformation(resultLog);
                }
                response.Success = false;
            }
            return new OkObjectResult(response);
        }
    }
}
