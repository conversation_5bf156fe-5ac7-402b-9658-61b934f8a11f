﻿namespace EvaluationValidationEcosystemFunction.Domain
{
    public class PaginationFilter
    {
        public int Offset { get; set; }
        public int Limit { get; set; }
        public PaginationFilter()
        {
            this.Offset = 0;
            this.Limit = 10;
        }
        public PaginationFilter(int offset, int limit)
        {
            this.Offset = Offset < 0 ? 0 : offset;
            this.Limit = Limit > 100 ? 100 : limit;
        }
    }
}
