﻿using System;
using System.Collections.Generic;
using System.Text;

namespace EvaluationValidationEcosystemFunction.Domain.Rule
{
	public class RuleDefinition
	{
		public int RuleDefinitionId { get; set; }
		public string FieldName { get; set; }
		public string Mandatory { get; set; }
		public string HasMinimum { get; set; }
		public string HasMaximum { get; set; }
		public decimal MinimumValue { get; set; }
		public decimal MaximumValue { get; set; }
		public string CreatedUser { get; set; }
		public DateTime ValidFrom { get; set; }
		public DateTime ValidTo { get; set; }
		//Int32 ROWVERSION { get; set; }
		public bool Enabled_ { get; set; }
		public string RuleType { get; set; }
		public string RegularExpression { get; set; }
		public int Attribute { get; set; }
		public string ErrorMessageEn { get; set; }		
		public string ErrorMessageFr { get; set; }
		public string Message { get; set; }
		public bool PositiveResultValue { get; set; }
		public string DependancyType { get; set; }
		public int DependentRuleDefinitionId { get; set; }
		public string HasDependancies { get; set; }
		public bool CheckDependenciesWithResult { get; set; }
		public List<RuleDefinitionLookUp> RuleDefinitionLookUps { get; set; }
		public List<RuleDefinitionCompare> RuleDefinitionCompares { get; set; }
		public string ResultLookUpField { get; set; }
		public string IncentiveLabelEn {get; set;}
		public int RuleOrder { get; set; }
	}

}
