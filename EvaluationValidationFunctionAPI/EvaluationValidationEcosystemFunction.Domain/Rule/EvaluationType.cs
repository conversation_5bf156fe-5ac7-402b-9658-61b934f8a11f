﻿using System;
using System.Collections.Generic;
using System.Text;
using EvaluationValidationEcosystemFunction.Domain.Evaluation;

namespace EvaluationValidationEcosystemFunction.Domain.Rule
{
    public class EvaluationType
    {
		public int FileTypeId { get; set; }
		public string Program { get; set; }
		public string FileType { get; set; }
		public string FileVersion { get; set; }
		public string CreatedUser { get; set; }
		public DateTime ValidFrom { get; set; }
		public DateTime ValidTo { get; set; }
		public string SubmissionType { get; set; }
		public List<RuleDefinition> RuleDefinitions { get; set; }
		public List<EvaluationXMLPathLookUp> EvaluationXMLPathLookUps { get; set; }
	}
}
