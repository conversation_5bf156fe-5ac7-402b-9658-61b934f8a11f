﻿using System;

namespace EvaluationValidationEcosystemFunction.Domain.Rule
{
    public class RunLog
    {
        public int? BatchId { get; set; }
        public int? EvaluationsId { get; set; }
        public string FileIdentifier { get; set; }
        public string FileType { get; set; }
        public DateTime? EvaluationDate { get; set; }
        public string FileVersion { get; set; }
        public string Program { get; set; }
        public int? RuleDefinitionId { get; set; }
        public DateTime? RunDateTime { get; set; }
        public string FieldValue { get; set; }
        public string RuleDefinitionResult { get; set; }        
        public string MessageEn { get; set; }
        public string MessageFr { get; set; }
        public string Message { get; set; }
        public string RuleRunError { get; set; }
           
        public RunLog(
        DateTime EvaluationDate,
        string Program,
        string FileType,
        string Version,
        string FileIdentifier,
        int BatchId
        )
        {
            
            this.EvaluationDate = EvaluationDate;
            this.Program = Program;
            this.FileType = FileType;
            this.FileVersion = Version;
            this.FileIdentifier = FileIdentifier;
            this.BatchId = BatchId;
        }
    }
}
