﻿using System;
using System.Collections.Generic;
using System.Text;

namespace EvaluationValidationEcosystemFunction.Domain.Rule
{
    public class RuleDefinitionLookUp
    {
		public int RuleDefinitionLookUpId { get; set; }
		public int RuleDefinitionId { get; set; }
		public string FieldName { get; set; }
		public string LookUpTableName { get; set; }
		public string CreatedUser { get; set; }
		public DateTime ValidFrom { get; set; }
		public DateTime ValidTo { get; set; }   		
		public string LookUpField { get; set; }
		public string LookupValueFieldName { get; set; }
		public string SubStringLogic { get; set; }
		public string SplitValue { get; set; }
		public string SplitNumber { get; set; }
		public string OverRideValue { get; set; }
		public string AdditionalLogic { get; set; }
		public int Attribute { get; set; }
	}
}
