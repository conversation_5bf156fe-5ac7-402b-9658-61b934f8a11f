﻿using System.Collections.Generic;

namespace EvaluationValidationEcosystemFunction.Domain.ManualEvaluation
{
    public class EvaluationRequest
    {
        public string UserId { set; get; }
        public int? BatchId { set; get; }
        public string QaAuditId { set; get; }
        public string ServiceOrganization { get; set; }
       
       
        public string RequestType { set; get; }
        public string SubRequestType { get; set; }
        public string FileNumber { get; set; }
        public string EvaluationFile { get; set; }
        public string EvaluationFilePath { get; set; }
        public string Rationale { get; set; }
        
        [IgnoreToDataTable]
        public IEnumerable<SupportingDocument> ManualEvaluationSuportDocs { get; set; }
    }
}
