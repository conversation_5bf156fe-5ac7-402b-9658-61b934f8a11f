﻿using System;
namespace EvaluationValidationEcosystemFunction.Domain.ManualEvaluation
{
    public class ManualEvaluationSearchParameter : PaginationFilter
    {
        public string Province { get; set; }
        public string ServiceOrganization { get; set; }
        public string Status { get; set; }
        public string TypeOfRequest { get; set; }

        public DateTime StartDate{ set; get; }
        public DateTime EndDate { set; get; }

        public string OrderBy { set; get; }
        public string OrderByField { set; get; }
    }
}
