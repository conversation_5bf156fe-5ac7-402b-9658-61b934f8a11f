﻿using System;
using System.Collections.Generic;
using System.Text;

namespace EvaluationValidationEcosystemFunction.Domain.Evaluation
{
    public class EvaluationIncentiveRecommendation
    {
        public string RecommendedIncentive { get; set; }
        public int Priority { get; set; }
        public EvaluationIncentiveRecommendation(string recommendedIncentive, int priority)
        {
            this.RecommendedIncentive = recommendedIncentive;
            this.Priority = priority;
        }
    }
}
