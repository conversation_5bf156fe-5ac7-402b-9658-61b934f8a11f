﻿using System;
using System.Collections.Generic;
using System.Text;

namespace EvaluationValidationEcosystemFunction.Domain.Evaluation
{
    public class ERSExtention
    {
        public int? EVAL_ID { get; set; }
        public string EVAL_TYPE { get; set; }

        public decimal? UGRCEILINGSERSRATING { get; set; }
        public decimal? UGRCEILINGCATHFLATERSRATING { get; set; }
        public decimal? UGRWALLSERSRATING
        { get; set; }
        public decimal? UGRFOUNDATIONERSRATING
        { get; set; }
        public decimal? UGRWINDOWSERSRATING
        { get; set; }
        public decimal? UGRDOORSERSRATING
        { get; set; }
        public decimal? UGREXPOSEDFLOORERSRATING
        { get; set; }
        public decimal? UGRAIRTIGHTNESSERSRATING
        { get; set; }
        public decimal? UGRHEATINGERSRATING
        { get; set; }
        public decimal? UGRCOOLINGERSRATING
        { get; set; }
        public decimal? UGRHOTWATERERSRATING
        { get; set; }
        public decimal? UGRVENTILATIONERSRATING
        { get; set; }
        public decimal? UGRGENERATIONERSRATING
        { get; set; }
        public decimal? HOCUGRCEILINGSERSRATING
        { get; set; }
        public decimal? HOCU<PERSON><PERSON><PERSON>INGCATHFLATERSRATING
        { get; set; }
        public decimal? HOCUGRWALLSERSRATING
        { get; set; }
        public decimal? HOCUGRFOUNDATIONERSRATING
        { get; set; }
        public decimal? HOCUGRWINDOWSERSRATING
        { get; set; }
        public decimal? HOCUGRDOORSERSRATING
        { get; set; }
        public decimal? HOCUGREXPOSEDFLOORERSRATING
        { get; set; }
        public decimal? HOCUGRAIRTIGHTNESSERSRATING
        { get; set; }
        public decimal? HOCUGRHEATINGERSRATING
        { get; set; }
        public decimal? HOCUGRCOOLINGERSRATING
        { get; set; }
        public decimal? HOCUGRHOTWATERERSRATING
        { get; set; }
        public decimal? HOCUGRVENTILATIONERSRATING
        { get; set; }
        public decimal? HOCUGRGENERATIONERSRATING
        { get; set; }
        public decimal? HOCERSHLAIR
        { get; set; }
        public decimal? HOCERSHLFOUND
        { get; set; }
        public decimal? HOCERSHLCEILING
        { get; set; }
        public decimal? HOCERSHLWALLS
        { get; set; }
        public decimal? HOCERSHLWINDOOR
        { get; set; }
        public decimal? HOCERSHLWINDOW
        { get; set; }
        public decimal? HOCERSHLDOOR
        { get; set; }
        public decimal? HOCERSHLEXPOSEDFLR
        { get; set; }
        public decimal? HOCUGRERSHLAIR
        { get; set; }
        public decimal? HOCUGRERSHLFOUND
        { get; set; }
        public decimal? HOCUGRERSHLCEILING
        { get; set; }
        public decimal? HOCUGRERSHLWALLS
        { get; set; }
        public decimal? HOCUGRERSHLWINDOOR
        { get; set; }
        public decimal? HOCUGRERSHLWINDOW
        { get; set; }
        public decimal? HOCUGRERSHLDOOR
        { get; set; }
        public decimal? HOCUGRERSHLEXPOSEDFLR
        { get; set; }
        public decimal? HOCERSSPACEHEATENERGY
        { get; set; }
        public decimal? HOCERSSPACECOOLENERGY
        { get; set; }
        public decimal? HOCERSWATERHEATINGENERGY
        { get; set; }
        public decimal? HOCERSVENTILATIONENERGY
        { get; set; }
        public decimal? HOCERSLIGHTAPPLIANCEENERGY
        { get; set; }
        public decimal? HOCERSOTHERELECENERGY
        { get; set; }
        public decimal? HOCUGRERSSPACEHEATENERGY
        { get; set; }
        public decimal? HOCUGRERSSPACECOOLENERGY
        { get; set; }
        public decimal? HOCUGRERSWATERHEATINGENERGY
        { get; set; }
        public decimal? HOCUGRERSVENTILATIONENERGY
        { get; set; }
        public decimal? HOCUGRERSLIGHTAPPLIANCEENERGY
        { get; set; }
        public decimal? HOCUGRERSOTHERELECENERGY
        { get; set; }
        public decimal? ERSPVAVAILABLEENERGY
        { get; set; }
        public decimal? UGRERSPVAVAILABLEENERGY
        { get; set; }
        public decimal? HOCERSCONELEC
        { get; set; }
        public decimal? HOCERSCONNGAS
        { get; set; }
        public decimal? HOCERSCONOIL
        { get; set; }
        public decimal? HOCERSCONPROP
        { get; set; }
        public decimal? HOCERSCONWOOD
        { get; set; }
        public decimal? HOCNUMBEROFOCCUPANTS
        { get; set; }
        public decimal? HOCTHERMOSTATHEATINGDAYTIME
        { get; set; }
        public decimal? HOCTHERMOSTATHEATINGNIGHTTIME
        { get; set; }
        public decimal? HOCCOOLINGSEASONMONTHS
        { get; set; }
        public decimal? HOCTHERMOSTATCOOLING
        { get; set; }
        public string HOCLIGHTINGENERGYSAVER
        { get; set; }
        public string HOCAPPLIANCESENERGYSAVER
        { get; set; }
        public string HOCCLOTHESWASHERENERGYSAVER
        { get; set; }
        public string HOCAPPLIED
        { get; set; }
        public decimal? ERSTOTALCONSGHG
        { get; set; }
        public decimal? ERSTOTALRENEWABLEGHG
        { get; set; }
        public decimal? ERSDESCOOLLOSS
        { get; set; }
        public decimal? UGRERSRENEWABLEPROD
        { get; set; }
        public decimal? UGRERSRENEWABLEELEC
        { get; set; }
        public decimal? UGRERSRENEWABLESOLAR
        { get; set; }
        public decimal? SOCHOTWATERLOAD
        { get; set; }
        public decimal? ROCHOTWATERLOAD
        { get; set; }
        public decimal? HOCAPPLIANCELOAD
        { get; set; }
        public decimal? HOCLIGHTINGLOAD
        { get; set; }
        public decimal? HOCOTHERELECTRICALLOAD
        { get; set; }
        public decimal? THERMOSTATHEATINGNIGHTTIME
        { get; set; }
        public decimal? COOLINGSEASONMONTHS
        { get; set; }
        public decimal? THERMOSTATCOOLING
        { get; set; }
        public decimal? SOCAPPLIANCELOAD
        { get; set; }
        public decimal? SOCLIGHTINGLOAD
        { get; set; }
        public decimal? SOCOTHERELECTRICALLOAD
        { get; set; }
        public decimal? HOTWATERTEMPERATURE
        { get; set; }
        public decimal? HOCHOTWATERLOAD
        { get; set; }
        public decimal? ROCAPPLIANCELOAD
        { get; set; }
        public decimal? ROCLIGHTINGLOAD
        { get; set; }
        public decimal? ROCOTHERELECTRICALLOAD
        { get; set; }
        public decimal? UTILIZEDSOLARGAINS
        { get; set; }
        public string FILE_NAME
        { get; set; }

        public decimal? RefHLAir { get; set; }
        public decimal? RefHLFound { get; set; }
        public decimal? RefHLCeiling { get; set; }
        public decimal? RefHLWalls { get; set; }
        public decimal? RefHLWinDoor { get; set; }
        public decimal? RefHLExposedFlr { get; set; }
        public decimal? RefHLWindow { get; set; }
        public decimal? RefHLDoor { get; set; }
        public decimal? RefDesHtLoss { get; set; }
        public decimal? RefDesCoolLoss { get; set; }
        public decimal? RefSpaceEnergy { get; set; }
        public decimal? RefSpaceCoolEnergy { get; set; }
        public decimal? RefWaterHeatEnergy { get; set; }
        public decimal? RefVentilationEnergy { get; set; }

    }
}
