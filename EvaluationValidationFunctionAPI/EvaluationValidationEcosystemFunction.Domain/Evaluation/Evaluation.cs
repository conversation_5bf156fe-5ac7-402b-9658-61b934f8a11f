﻿using System;
using System.Collections.Generic;
using System.Text;

namespace EvaluationValidationEcosystemFunction.Domain.Evaluation
{
    public class Evaluation
    {
        public int? EVALUATIONSID { get; set; }
        public int? BatchId { get; set; }
        public string FILENUMBER { get; set; }
        public string PREVIOUS<PERSON>LEID { get; set; }
        public string EVALUATIONSTATUS { get; set; }
        public DateTime CREATIONDATE { get; set; }
        public DateTime MODIFICATIONDATE { get; set; }
        public int? YEARBUILT { get; set; }
        public string CLIENTCITY { get; set; }
        public string CLIENTADDR { get; set; }
        public string BUILDER { get; set; }
        public string HOUSEREGION { get; set; }
        public string WEATHERLOC { get; set; }
        public string ENTRYBY { get; set; }
        public string CLIENTNAME { get; set; }
        public string TELEPHONE { get; set; }
        public decimal? FLOORAREA { get; set; }
        public decimal? FOOTPRINT { get; set; }
        public string FURNACETYPE { get; set; }
        public decimal? FURSSEFF { get; set; }
        public string FURNACEFUEL { get; set; }
        public string HPSOURCE { get; set; }
        public decimal? COP { get; set; }
        public string PDHWTYPE { get; set; }
        public decimal? PDHWEF { get; set; }
        public string PDHWFUEL { get; set; }
        public string DHWHPTYPE { get; set; }
        public decimal? DHWHPCOP { get; set; }
        public decimal? CSIA { get; set; }
        public string TYPEOFHOUSE { get; set; }
        public decimal? CEILINS { get; set; }
        public decimal? FNDWALLINS { get; set; }
        public decimal? MAINWALLINS { get; set; }
        public string STOREYS { get; set; }
        public int? TOTALOCCUPANTS { get; set; }
        public string PLANSHAPE { get; set; }
        public decimal? TBSMNT { get; set; }
        public decimal? TMAIN { get; set; }
        public decimal? HSEVOL { get; set; }
        public decimal? AIR50P { get; set; }
        public decimal? LEAKAR { get; set; }
        public string CENVENTSYSTYPE { get; set; }
        public string REGISTRATION { get; set; }
        public string PROGRAMNAME { get; set; }
        public decimal? EGHFCONELEC { get; set; }
        public decimal? EGHFCONNGAS { get; set; }
        public decimal? EGHFCONOIL { get; set; }
        public decimal? EGHFCONPROP { get; set; }
        public decimal? EGHFCONTOTAL { get; set; }
        public decimal? EGHSPACEENERGY { get; set; }
        public decimal? EGHFCOSTELEC { get; set; }
        public decimal? EGHFCOSTNGAS { get; set; }
        public decimal? EGHFCOSTOIL { get; set; }
        public decimal? EGHFCOSTPROP { get; set; }
        public decimal? EGHFCOSTTOTAL { get; set; }
        public decimal? EGHCRITNATACH { get; set; }
        public decimal? EGHCRITTOTACH { get; set; }
        public decimal? EGHHLAIR { get; set; }
        public decimal? EGHHLFOUND { get; set; }
        public decimal? EGHHLCEILING { get; set; }
        public decimal? EGHHLWALLS { get; set; }
        public decimal? EGHHLWINDOOR { get; set; }
        public decimal? EGHRATING { get; set; }
        public string UGRFURNACETYP { get; set; }
        public decimal? UGRFURNACEEFF { get; set; }
        public string UGRFURNACEFUEL { get; set; }
        public string UGRHPTYPE { get; set; }
        public decimal? UGRHPCOP { get; set; }
        public string UGRDHWSYSTYPE { get; set; }
        public decimal? UGRDHWSYSEF { get; set; }
        public string UGRDHWSYSFUEL { get; set; }
        public string UGRDHWHPTYPE { get; set; }
        public decimal? UGRDHWHPCOP { get; set; }
        public decimal? UGRDHWCSIA { get; set; }
        public decimal? UGRCEILINS { get; set; }
        public decimal? UGRFNDINS { get; set; }
        public decimal? UGRWALLINS { get; set; }
        public decimal? UGRFCONELEC { get; set; }
        public decimal? UGRFCONNGAS { get; set; }
        public decimal? UGRFCONOIL { get; set; }
        public decimal? UGRFCONPROP { get; set; }
        public decimal? UGRFCONTOTAL { get; set; }
        public decimal? UGRFCOSTELEC { get; set; }
        public decimal? UGRFCOSTNGAS { get; set; }
        public decimal? UGRFCOSTOIL { get; set; }
        public decimal? UGRFCOSTPROP { get; set; }
        public decimal? UGRFCOSTTOTAL { get; set; }
        public decimal? UGRAIR50PA { get; set; }
        public decimal? UGRHLAIR { get; set; }
        public decimal? UGRHLFOUND { get; set; }
        public decimal? UGRHLCEILING { get; set; }
        public decimal? UGRHLWALLS { get; set; }
        public decimal? UGRHLWINDOOR { get; set; }
        public decimal? UGRRATING { get; set; }
        public string PROVINCE { get; set; }
        public int? DECADEBUILT { get; set; }
        public string CLIENTPCODE { get; set; }
        public int? LOCATIONID { get; set; }
        public decimal? EGHFURNACEAEC { get; set; }
        public decimal? UGRFURNACEAEC { get; set; }
        public decimal? EGHDESHTLOSS { get; set; }
        public decimal? UGRDESHTLOSS { get; set; }
        public int? EGHFURSEAEFF { get; set; }
        public int? UGRFURSEAEFF { get; set; }
        public string UCEVENTSYSTYPE { get; set; }
        public decimal? UGRCRITNATACH { get; set; }
        public decimal? EGHHLEXPOSEDFLR { get; set; }
        public decimal? EGHINEXPOSEDFLR { get; set; }
        public decimal? UGRINEXPOSEDFLR { get; set; }
        public decimal? UGRHLEXPOSEDFLR { get; set; }
        public decimal? UGRCRITTOTACH { get; set; }
        public decimal? UGRFURSEASEFF { get; set; }
        public decimal? EGHFURSEASEFF { get; set; }
        public int? BATCHNUMBER { get; set; }
        public string PAYABLE { get; set; }
        public decimal? EGHFCONWOOD { get; set; }
        public decimal? EGHFCOSTWOOD { get; set; }
        public decimal? UGRFCONWOOD { get; set; }
        public decimal? UGRFCOSTWOOD { get; set; }
        public string INFO1 { get; set; }
        public string INFO2 { get; set; }
        public string INFO3 { get; set; }
        public string INFO4 { get; set; }
        public string INFO5 { get; set; }
        public string INFO6 { get; set; }
        public string OTC { get; set; }
        public string VERMICULITE { get; set; }
        public int? PONYWALLEXISTS { get; set; }
        public decimal? BASEMENTFLOORAR { get; set; }
        public decimal? WALKOUTFLOORAR { get; set; }
        public decimal? CRAWLSPFLOORAR { get; set; }
        public decimal? SLABFLOORAR { get; set; }
        public string BLOWERDOORTEST { get; set; }
        public decimal? FIREPLACEDAMP1 { get; set; }
        public decimal? FIREPLACEDAMP2 { get; set; }
        public decimal? HEATSYSSIZEOP { get; set; }
        public decimal? TOTALVENTSUPPLY { get; set; }
        public decimal? TOTALVENTEXH { get; set; }
        public decimal? UGRTOTALVENTSUP { get; set; }
        public decimal? UGRTOTALVENTEXH { get; set; }
        public decimal? CREDITPV { get; set; }
        public decimal? CREDITWIND { get; set; }
        public decimal? UGRCREDITPV { get; set; }
        public decimal? UGRCREDITWIND { get; set; }
        public decimal? CREDITTHERMST { get; set; }
        public decimal? CREDITVENT { get; set; }
        public decimal? CREDITGARAGE { get; set; }
        public decimal? CREDITLIGHTING { get; set; }
        public decimal? CREDITEGH { get; set; }
        public decimal? CREDITOTH1OTH2 { get; set; }
        public string WINDOWCODE { get; set; }
        public string UGRWINDOWCODE { get; set; }
        public decimal? HRVEFF0C { get; set; }
        public int? UNITSMURBS { get; set; }
        public int? VISITEDUNITS { get; set; }
        public int? BASELOADSMURB { get; set; }
        public string MURBHTSYSTEMDIS { get; set; }
        public string INDFURNACETYPE { get; set; }
        public decimal? INDFURSSEFF { get; set; }
        public string INDFURNACEFUEL { get; set; }
        public string UGRINDFURNACETP { get; set; }
        public decimal? UGRINDFURSSEFF { get; set; }
        public string UGRINDFURNACEFU { get; set; }
        public string SHAREDATA { get; set; }
        public string ESTAR { get; set; }
        public decimal? DEPRESSEXHAUST { get; set; }
        public DateTime ENTRYDATE { get; set; }
        public string FURNACEMODEL { get; set; }
        public string BUILDERNAME { get; set; }
        public string OWNERSHIP { get; set; }
        public int? EGHHEATFCONSE { get; set; }
        public int? EGHHEATFCONSG { get; set; }
        public int? EGHHEATFCONSO { get; set; }
        public int? EGHHEATFCONSP { get; set; }
        public int? EGHHEATFCONSW { get; set; }
        public int? UGRHEATFCONSE { get; set; }
        public int? UGRHEATFCONSG { get; set; }
        public int? UGRHEATFCONSO { get; set; }
        public int? UGRHEATFCONSP { get; set; }
        public int? UGRHEATFCONSW { get; set; }
        public int? FURDCMOTOR { get; set; }
        public int? UGRFURDCMOTOR { get; set; }
        public string HPESTAR { get; set; }
        public string UGRHPESTAR { get; set; }
        public int? NELECTHERMOS { get; set; }
        public int? UGRNELECTHERMOS { get; set; }
        public string EPACSA { get; set; }
        public string UGREPACSA { get; set; }
        public string SUPPHTGTYPE1 { get; set; }
        public string SUPPHTGTYPE2 { get; set; }
        public string SUPPHTGFUEL1 { get; set; }
        public string SUPPHTGFUEL2 { get; set; }
        public string UGRSUPPHTGTYPE1 { get; set; }
        public string UGRSUPPHTGTYPE2 { get; set; }
        public string UGRSUPPHTGFUEL1 { get; set; }
        public string UGRSUPPHTGFUEL2 { get; set; }
        public string EPACSASUPPHTG1 { get; set; }
        public string EPACSASUPPHTG2 { get; set; }
        public string UEPACSASUPPHTG1 { get; set; }
        public string UEPACSASUPPHTG2 { get; set; }
        public string HVIEQUIP { get; set; }
        public string UGRHVIEQUIP { get; set; }
        public string AIRCONDTYPE { get; set; }
        public string UGRAIRCONDTYPE { get; set; }
        public decimal? AIRCOP { get; set; }
        public decimal? UGRAIRCOP { get; set; }
        public string ACCENTESTAR { get; set; }
        public string UGRACCENTESTAR { get; set; }
        public int? ACWINDESTAR { get; set; }
        public int? UGRACWINDESTAR { get; set; }
        public string FNDHDR { get; set; }
        public string UGRFNDHDR { get; set; }
        public int? NUMWINDOWS { get; set; }
        public int? NUMWINESTAR { get; set; }
        public int? NUMDOORS { get; set; }
        public int? UGRNUMWINESTAR { get; set; }
        public int? NUMDOORESTAR { get; set; }
        public int? UGRNUMDOORESTAR { get; set; }
        public int? ACWINDNUM { get; set; }
        public int? UGRACWINDNUM { get; set; }
        public decimal? HEATAFUE { get; set; }
        public decimal? UGRHEATAFUE { get; set; }
        public string CEILINGTYPE { get; set; }
        public string UGRCEILINGTYPE { get; set; }
        public string ATTICCEILINGDEF { get; set; }
        public string UATTCEILINGDEF { get; set; }
        public string CAFLACEILINGDEF { get; set; }
        public string UCAFLCEILINGDEF { get; set; }
        public string FNDTYPE { get; set; }
        public string UGRFNDTYPE { get; set; }
        public string FNDDEF { get; set; }
        public string UGRFNDDEF { get; set; }
        public string WALLDEF { get; set; }
        public string UGRWALLDEF { get; set; }
        public int? EINCENTIVE { get; set; }
        public int? LFTOILETS { get; set; }
        public int? ULFTOILETS { get; set; }
        public int? DWHRL1M { get; set; }
        public int? UDWHRL1M { get; set; }
        public int? DWHRM1M { get; set; }
        public int? UDWHRM1M { get; set; }
        public string WTHDATA { get; set; }
        public string SDHWTYPE { get; set; }
        public decimal? SDHWEF { get; set; }
        public string SDHWFUEL { get; set; }
        public string SDHWHPTYPE { get; set; }
        public decimal? SDHWHPCOP { get; set; }
        public string UGRSDHWSYSTYPE { get; set; }
        public decimal? UGRSDHWSYSEF { get; set; }
        public string UGRSDHWSYSFUEL { get; set; }
        public string UGRSDHWHPTYPE { get; set; }
        public decimal? UGRSDHWHPCOP { get; set; }
        public string EXPOSEDFLOOR { get; set; }
        public string UGEXPOSEDFLOOR { get; set; }
        public int? MURBHSESTAR { get; set; }
        public int? MURBWOODEPA { get; set; }
        public int? MURBASHPESTAR { get; set; }
        public int? MURBDWHRL1M { get; set; }
        public int? MURBDWHRM1M { get; set; }
        public int? MURBHRVHVI { get; set; }
        public int? MURBDHWINS { get; set; }
        public int? MURBDHWCOND { get; set; }
        public int? MURBWOODHEAT { get; set; }
        public string MAILADDR { get; set; }
        public string MAILCITY { get; set; }
        public string MAILREGION { get; set; }
        public string MAILPCODE { get; set; }
        public string TAXNUMBER { get; set; }
        public string INFO7 { get; set; }
        public string INFO8 { get; set; }
        public string INFO9 { get; set; }
        public string INFO10 { get; set; }
        public decimal? TYPE1CAPACITY { get; set; }
        public string PDHWESTAR { get; set; }
        public string UGRPDHWESTAR { get; set; }
        public string SDHWESTAR { get; set; }
        public string UGRSDHWESTAR { get; set; }
        public int? MURBDHWINSES { get; set; }
        public int? UMURBDHWINSES { get; set; }
        public int? MURBDHWCONDINSES { get; set; }
        public int? UMURBDHWCONDINES { get; set; }
        public decimal? HPCAP { get; set; }
        public string ACMODELNUMBER { get; set; }
        public string MIXUSE { get; set; }
        public string WINDOWCODENUM { get; set; }
        public string UWINDOWCODENUM { get; set; }
        public string CID { get; set; }
        public int? NUMSOLSYS { get; set; }
        public int? TOTCSIA { get; set; }
        public int? LARGESTCSIA { get; set; }
        public string SNDHEATSYS { get; set; }
        public string SNDHEATSYSFUEL { get; set; }
        public string SNDHEATSYSTYPE { get; set; }
        public int? SNDHEATAFUE { get; set; }
        public string SNDHEATDCMOTOR { get; set; }
        public string SNDHEATMANUFACTURER { get; set; }
        public string SNDHEATMODEL { get; set; }
        public string SNDHEATESTAR { get; set; }
        public string UGRSNDHEATSYS { get; set; }
        public string UGRSNDHEATSYSFUEL { get; set; }
        public string UGRSNDHEATSYSTYPE { get; set; }
        public int? UGRSNDHEATAFUE { get; set; }
        public string UGRSNDHEATDCMOTOR { get; set; }
        public string UGRSNDHEATMANUFACTURER { get; set; }
        public string UGRSNDHEATMODEL { get; set; }
        public string UGRSNDHEATESTAR { get; set; }
        public int? NUMWINZONED { get; set; }
        public int? NUMDOORZONED { get; set; }
        public int? UGRNUMWINZONED { get; set; }
        public int? UGRNUMDOORZONED { get; set; }
        public string WASHERMANUFACTURER { get; set; }
        public string WASHERMODEL { get; set; }
        public string WASHERESTAR { get; set; }
        public string UGRWASHERMANUFACTURER { get; set; }
        public string UGRWASHERMODEL { get; set; }
        public string UGRWASHERESTAR { get; set; }
        public string DRYERFUEL { get; set; }
        public string DRYERMANUFACTURER { get; set; }
        public string DRYERMODEL { get; set; }
        public string UGRDRYERFUEL { get; set; }
        public string UGRDRYERMANUFACTURER { get; set; }
        public string UGRDRYERMODEL { get; set; }
        public int? ESTARLIGHTS { get; set; }
        public int? UGRESTARLIGHTS { get; set; }
        public string HVIESTAR { get; set; }
        public int? ESTARMURBHRVHVI { get; set; }
        public string UGRHVIESTAR { get; set; }
        public int? UGRMURBHRVHVI { get; set; }
        public int? UGRESTARMURBHRVHVI { get; set; }
        public int? MURBDHWSTES { get; set; }
        public int? UGRMURBDHWSTES { get; set; }
        public string EVALTYPE { get; set; }
        public int? EID { get; set; }
        public int? HOUSEID { get; set; }
        public string JUSTIFY { get; set; }
        public int? ERSRATING { get; set; }
        public int? UGRERSRATING { get; set; }
        public decimal? ERSENERGYINTENSITY { get; set; }
        public decimal? UGRERSENERGYINTENSITY { get; set; }
        public decimal? ERSGHG { get; set; }
        public decimal? UGRERSGHG { get; set; }
        public decimal? ERSRENEWABLEPROD { get; set; }
        public string HOCERSRATING { get; set; }
        public string HOCUGRERSRATING { get; set; }
        public int? ERSREFHOUSERATING { get; set; }
        public string RULESETVER { get; set; }
        public string RULESETTYPE { get; set; }
        public decimal? HEATEDFLOORAREA { get; set; }
        public decimal? ERSRENEWABLEELEC { get; set; }
        public decimal? ERSSPACECOOLENERGY { get; set; }
        public decimal? ERSRENEWABLESOLAR { get; set; }
        public decimal? ERSWATERHEATINGENERGY { get; set; }
        public decimal? ERSVENTILATIONENERGY { get; set; }
        public decimal? ERSLIGHTAPPLIANCEENERGY { get; set; }
        public decimal? ERSOTHERELECENERGY { get; set; }
        public decimal? UGRERSSPACECOOLENERGY { get; set; }
        public decimal? UGRERSWATERHEATINGENERGY { get; set; }
        public decimal? UGRERSVENTILATIONENERGY { get; set; }
        public decimal? UGRERSLIGHTAPPLIANCEENERGY { get; set; }
        public decimal? UGRERSOTHERELECENERGY { get; set; }
        public decimal? ERSELECGHG { get; set; }
        public decimal? ERSNGASGHG { get; set; }
        public decimal? ERSOILGHG { get; set; }
        public decimal? ERSPROPGHG { get; set; }
        public decimal? ERSWOODGHG { get; set; }
        public decimal? ERSRENEWABLEELECGHG { get; set; }
        public decimal? ERSRENEWABLESOLARGHG { get; set; }
        public decimal? ERSHLWINDOW { get; set; }
        public decimal? ERSHLDOOR { get; set; }
        public decimal? UGRERSHLWINDOW { get; set; }
        public decimal? UGRERSHLDOOR { get; set; }
        public decimal? UGRSPACEENERGY { get; set; }
        public decimal? QWARN { get; set; }
        public decimal? QTOT { get; set; }
        public string DATASET { get; set; }
        public string EIDEF { get; set; }
        public string UGREIDEF { get; set; }
        public string BUILDINGTYPE { get; set; }
        public decimal? EGHFCONWOODGJ { get; set; }
        public int? NUMBUILDINGSTOREYS { get; set; }
        public int? NUMDWELLINGUNITS { get; set; }
        public int? NUMNONRESUNITS { get; set; }
        public decimal? NONRESHEATEDFLOORAREA { get; set; }
        public decimal? COMMONHEATEDFLOORAREA { get; set; }
        public int? MURBSOCMULTIPLIER { get; set; }
        public string ATYPICALENERGYLOADS { get; set; }
        public string ELECAUTOCHRGSTATION { get; set; }
        public decimal? COMMONSPACEELECCONS { get; set; }
        public int? AUXENERGY { get; set; }
        public decimal? ERSREFHOUSEGHG { get; set; }
        public int? BUILDINGTYPEID { get; set; }
        public string AUTHID { get; set; }
        public decimal? PDHWUEF { get; set; }
        public string PDRAWPATTERN { get; set; }
        public string UGRPDRAWPATTERN { get; set; }
        public decimal? UGRPDHWUEF { get; set; }
        public decimal? SDHWUEF { get; set; }
        public string SDRAWPATTERN { get; set; }
        public string UGRSDRAWPATTERN { get; set; }
        public decimal? UGRSDHWUEF { get; set; }
        public string UCENVENTSYSTYPE { get; set; }
        public string PRIDHWMODEL { get; set; }
        public decimal? NLA { get; set; }
        public decimal? SLABFLOORAREA { get; set; }
        public int? UNITSCONNECTEDDWHR { get; set; }
        public int? EUGRCATHEDRALCEILFLATPRIORITY { get; set; }
        public int? EUGRCEILINGSPRIORITY { get; set; }
        public int? EUGRMAINWALLSPRIORITY { get; set; }
        public int? EUGRFOUNDATIONPRIORITY { get; set; }
        public int? EUGRFLOORPRIORITY { get; set; }
        public int? EUGRAIRTIGHTNESSPRIORITY { get; set; }
        public int? EUGRDOORSPRIORITY { get; set; }
        public int? EUGRHEATINGPRIORITY { get; set; }
        public int? EUGRHOTWATERPRIORITY { get; set; }
        public int? EUGRVENTILATIONPRIORITY { get; set; }
        public int? EUGRCOOLINGPRIORITY { get; set; }
        public string MURBFURNACEFUEL { get; set; }
        public string MURBFURNACETYPE { get; set; }
        public string MURBFURSSEFF { get; set; }
        public string MURBHEATAFUE { get; set; }
        public string MURBFURDCMOTOR { get; set; }
        public int? EUGROTHELECPRIORITY { get; set; }
        public int? EUGRWINDOWSPRIORITY { get; set; }
        public string SYSGUID { get; set; }
        public string EXPOSEDFLOORDEF { get; set; }
        public string UGREXPOSEDFLOORDEF { get; set; }
        public string NLR { get; set; }
        public decimal? NBCANNUALENEGYCONSUMPTION { get; set; }
        public decimal? NBCHOUSEENERGYTARGET { get; set; }
        public decimal? OVERALLIMPROVEMENT { get; set; }
        public decimal? ENVELOPEIMPROVEMENT { get; set; }
        public int? AIRTIGHTNESSLEVEL { get; set; }
        public decimal? ENERGYPERFORMANCETIER { get; set; }
        public decimal? TEDI { get; set; }
        public decimal? MEUI { get; set; }
        public string INSCOPEOFNBC { get; set; }
        public string PEAKCOOLINGVALIDATION { get; set; }
        public int AHRI { get; set; }
        public decimal KWPV { get; set; }
        public decimal UGRKWPV { get; set; }
        public string BATTERYSTORAGE { get; set; }
        public string UGRBATTERYSTORAGE { get; set; }
        public string GREENERHOMES { get; set; }
        public string BACKWATERVALVE { get; set; }
        public char UGRBACKWATERVALVE { get; set; }
        public string SUMPPUMP { get; set; }
        public char UGRSUMPPUMP { get; set; }
        public string PROGSMARTTHERMOSTAT { get; set; }
        public char UGRPROGSMARTTHERMOSTAT { get; set; }
        public decimal EVALUATIONCOST { get; set; }
        public string ROOFINGMEMBRANE { get; set; }
        public char UGRROOFINGMEMBRANE { get; set; }
        public string SLABINSUL { get; set; }
        public char UGRSLABINSUL { get; set; }
        public string WATERPROOF { get; set; }
        public char UGRWATERPROOF { get; set; }
        public string MOISTUREPROOFCS { get; set; }
        public char UGRMOISTUREPROOFCS { get; set; }
        public string MINR10EXPFLOOR { get; set; }
        public char UGRMINR10EXPFLOOR { get; set; }
        public int NUMWINU122 { get; set; }
        public int UGRNUMWINU122 { get; set; }
        public int NUMWINU105 { get; set; }
        public int UGRNUMWINU105 { get; set; }
        public int NUMER40PLUS { get; set; }
        public int UGRNUMER40PLUS { get; set; }
        public int NUMER34TO39 { get; set; }
        public int UGRNUMER34TO39 { get; set; }
        public int NUMBEROFHEADS { get; set; }
        public int UGRNUMBEROFHEADS { get; set; }
        public string CCASHP { get; set; }
        public string UGRCCASHP { get; set; }
        public decimal CCASHPCAP { get; set; }
        public decimal CCASHPSEER { get; set; }
        public decimal CCASHPHSPF { get; set; }
        public decimal CCASHPCOP { get; set; }
        public decimal CCASHPCAPACITYMAINTENANCE { get; set; }
        public decimal? NUMHPWHMURB { get; set; }
        public decimal? PRIMARYDHWTANKVOLUME { get; set; }
        public decimal? SECONDARYDHWTANKVOLUME { get; set; }
        public string REMOTECOMMUNITIES { get; set; }
        public string GUARDED { get; set; }
        public string GHGI { get; set; }
        public string ELGNBCCMP { get; set; }

        public int? DWHRhor { get; set; }
        public int? DWHRvert { get; set; }
        public int? UDWHRhor { get; set; }
        public int? UDWHRvert { get; set; }
        public string ApplicationNumber { get; set; }

        public string IDNUMBER { get; set; }
        public string PARTNER { get; set; }
        public string EVALUATOR { get; set; }


    }
}
