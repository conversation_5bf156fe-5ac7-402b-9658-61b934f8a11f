﻿using System;
using System.Collections.Generic;
using System.Text;

namespace EvaluationValidationEcosystemFunction.Domain.Evaluation
{
    public class EvaluationXMLPathLookUp
    {
        public int EvaluationXMLPathLookUpId { get; set; }
        public int FileTypeId { get; set; }
        public string FieldName { get; set; }
        public string XMLPath { get; set; }
        public int Attribute { get; set; }
        public string CreatedUser { get; set; }
        public DateTime ValidFrom { get; set; }
        public DateTime ValidTo { get; set; }
        public string TableName { get; set; }
    }
}
