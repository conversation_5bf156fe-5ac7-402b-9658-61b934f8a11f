﻿using System;
using System.Collections.Generic;
using System.Text;

namespace EvaluationValidationEcosystemFunction.Domain.Request
{
    public class EvaluationFile
    {
        public int EvaluationFileBatchId { get; set; }
        public string EvaluationFileName { get; set; }
        public string ReceivedEvaluationFileName { get; set; }
        public string ReceivedDirectoryName { get; set; }
        public string ADLLandingContainerName { get; set; }
        public string ADLLandingDirectoryName { get; set; }
        public string ADLContainerName { get; set; }
        public string ADLDirectoryName { get; set; }
        public string ADLArchiveContainerName { get; set; }
        public string ADLArchiveDirectoryName { get; set; }
        public string SubmissionType { get; set; }
    }
}
