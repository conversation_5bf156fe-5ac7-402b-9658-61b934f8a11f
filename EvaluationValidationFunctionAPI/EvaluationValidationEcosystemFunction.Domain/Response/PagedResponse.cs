﻿namespace EvaluationValidationEcosystemFunction.Domain.Response
{
    public class PagedResponse<T> : ServiceResponse<T>
    {
        public PagedResponse(T data, int offset, int limit, int totalRecords)
        {
            Data = data;
            Success = true;
            TotalRecords = totalRecords;
            Offset = offset;
            Limit = limit;
        }
        public int TotalRecords { get; private set; }
        public int Offset { get; private set; }
        public int Limit { get; private set; }
    }
}
