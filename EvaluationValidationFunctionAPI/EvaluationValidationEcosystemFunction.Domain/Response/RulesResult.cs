﻿using System;
using System.Collections.Generic;
using System.Text;
using EvaluationValidationEcosystemFunction.Domain.Request;
using EvaluationValidationEcosystemFunction.Domain.Rule;
using EvaluationValidationEcosystemFunction.Domain.Evaluation;
using System.Data;

namespace EvaluationValidationEcosystemFunction.Domain.Response
{
    public class RulesResult
    {
        public string FileType { get; set; }
        public int? BatchId { get; set; }

        public int? EvaluationFileBatchId { get; set; }

        public string EvaluationStatus { get; set; }                
        public List<string> Logs { get; set; }
        public EvaluationValidationEcosystemFunction.Domain.Evaluation.Evaluation Evaluation { get; set; }
        public List<UpdateEvaluationDifference> UpdateEvaluationDifference { get; set; }
        public List <EvaluationIncentiveResult> EvaluationIncentiveResults { get; set; }
        public EvaluationFile EvaluationFile { get; set; }
        public List<RunLog> RunLog { get; set; }
        public List<EvaluationIncentiveRecommendation> EvaluationIncentiveRecommendations { get; set; }
        public RulesResult()
        {
            this.Logs = new List<string>();
            this.UpdateEvaluationDifference = new List<UpdateEvaluationDifference>();
            this.EvaluationIncentiveResults = new List<EvaluationIncentiveResult>();
            this.RunLog = new List<RunLog>();
        }
    }    
}
