﻿using AutoMapper;
using EveFileAPI.Domain.Blob;
using EveFileAPI.Domain.FileInfo;
using EveFileAPI.ViewModel.FileInfo;
using Microsoft.AspNetCore.Http;

namespace EveFileAPI.Core.ProfileMapper
{
    public class EvaluationFileUpdateConverter : ITypeConverter<EvaluationFileUpdateVM, EvaluationFile>
    {
        public EvaluationFile Convert(EvaluationFileUpdateVM source, EvaluationFile destination, ResolutionContext context)
        {
            destination = new EvaluationFile() { SubmissionType = source.SubmissionType,ChannelName=source.ChannelName };
            if (source.EvaluationFile != null)
                destination.Name = source.EvaluationFile.FileName;
            destination.SupportingDocument = new System.Collections.Generic.List<SupportingDocument>();
            destination.HomeOwnerDocument = new System.Collections.Generic.List<SupportingDocument>();

            if (source.SupportingDocument != null)
            {
                foreach (var item in source.SupportingDocument)
                {
                    destination.SupportingDocument.Add(new SupportingDocument() { Name = item.FileName });
                }
            }
            if (source.HomeOwnerDocument != null)
            {
                destination.HomeOwnerDocument = new System.Collections.Generic.List<SupportingDocument>();
                foreach (var item in source.HomeOwnerDocument)
                {
                    destination.HomeOwnerDocument.Add(new SupportingDocument() { Name = item.FileName });
                }
            }
            return destination;
        }
    }
}
