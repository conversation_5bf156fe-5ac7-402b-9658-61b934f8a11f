﻿using AutoMapper;
using Azure.Storage.Blobs.Models;
using EveFileAPI.Domain.Blob;
using EveFileAPI.Domain.Evaluation;
using EveFileAPI.Domain.FileInfo;
using EveFileAPI.ViewModel.Evaluation;
using EveFileAPI.ViewModel.FileInfo;
using Microsoft.AspNetCore.Http;

namespace EveFileAPI.Core.ProfileMapper
{
    public class ProfileAutoMapper : Profile
    {
        public ProfileAutoMapper()
        {
            CreateMap<IFormFile, BlobData>().ConvertUsing<FormFileTypeConverter>();
            CreateMap<IFormFile, SupportingDocument>().ForMember(d => d.Name, f => f.MapFrom(s => s.FileName));
            CreateMap<EvaluationFileVM, EvaluationFile>().ConvertUsing<EvaluationFileTypeConverter>();

            CreateMap<EvaluationFileUpdateVM, EvaluationFile>().ConvertUsing<EvaluationFileUpdateConverter>();
            CreateMap<BlobDownloadInfo, BlobData>().ConvertUsing<BlobFileTypeConverter>();
            CreateMap<VEvaluationFileBatch, EvaluationFileBatch>()
            .ForMember(d => d.ADLArchiveContainerName, f => f.MapFrom(s => s.AdlarchiveContainerName))
            .ForMember(d => d.ADLArchiveDirectoryName, f => f.MapFrom(s => s.AdlarchiveDirectoryName))
            .ForMember(d => d.ADLContainerName, f => f.MapFrom(s => s.AdlcontainerName))
            .ForMember(d => d.ADLDirectoryName, f => f.MapFrom(s => s.AdldirectoryName))
            .ForMember(d => d.ADLLandingContainerName, f => f.MapFrom(s => s.AdllandingContainerName))
            .ForMember(d => d.ADLLandingDirectoryName, f => f.MapFrom(s => s.AdllandingDirectoryName))
            .ForMember(d => d.EvaluationFileBatchId, f => f.MapFrom(s => s.EvaluationFileBatchId))
            .ForMember(d => d.EvaluationFileName, f => f.MapFrom(s => s.EvaluationFileName))
            .ForMember(d => d.ReceivedDirectoryName, f => f.MapFrom(s => s.ReceivedDirectoryName))
            .ForMember(d => d.ReceivedEvaluationFileName, f => f.MapFrom(s => s.ReceivedEvaluationFileName))
            .ForMember(d => d.BatchId, f => f.MapFrom(s => s.BatchId))
            .ForMember(d => d.SubmissionType, f => f.MapFrom(s => s.SubmissionType))
            .ForMember(d => d.SupportingDocumentDirectory, f => f.Ignore());

            CreateMap<EvaluationBatch, EvaluationBulkFileVM>();
            CreateMap<string, EvaluationFile>().ConvertUsing<StringToEvaluationFileUpdateConverter>();
            CreateMap<EvaluationFileBatch, EvaluationVM>().ConvertUsing<EvaluationFileBatchToEvaluationVMConverter>();


        }
    }
}
