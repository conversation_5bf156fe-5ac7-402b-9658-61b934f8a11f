﻿using AutoMapper;
using EveFileAPI.Domain.FileInfo;
using EveFileAPI.ViewModel.Evaluation;

namespace EveFileAPI.Core.ProfileMapper
{
    public class EvaluationFileBatchToEvaluationVMConverter : ITypeConverter<EvaluationFileBatch, EvaluationVM>
    {
        public EvaluationVM Convert(EvaluationFileBatch source, EvaluationVM destination, ResolutionContext context)
        {
            if (destination == null)
                destination = new EvaluationVM();

            destination.ADLArchiveContainerName = source.ADLArchiveContainerName;
            destination.ADLArchiveDirectoryName = source.ADLArchiveDirectoryName;
            destination.ADLContainerName = source.ADLContainerName;
            destination.ADLDirectoryName = source.ADLDirectoryName;
            destination.ADLLandingContainerName = source.ADLLandingContainerName;
            destination.ADLLandingDirectoryName = source.ADLLandingDirectoryName;
            destination.EvaluationFileBatchId = source.EvaluationFileBatchId;
            destination.EvaluationFileName = source.EvaluationFileName;
            destination.ReceivedDirectoryName = source.ReceivedDirectoryName;
            destination.ReceivedEvaluationFileName = source.ReceivedEvaluationFileName;
            destination.SupportingDocumentDirectory = source.SupportingDocumentDirectory;
            destination.HomeOwnerReportDirectory = source.HomeOwnerReportDirectory;
            return destination;
        }
    }
}
