﻿using AutoMapper;
using EveFileAPI.Domain.FileInfo;
using EveFileAPI.ViewModel.FileInfo;

namespace EveFileAPI.Core.ProfileMapper
{
    public class EvaluationFileTypeConverter : ITypeConverter<EvaluationFileVM, EvaluationFile>
    {
        public EvaluationFile Convert(EvaluationFileVM source, EvaluationFile destination, ResolutionContext context)
        {              
            destination = new EvaluationFile() { SubmissionType=source.SubmissionType, Name=source.EvaluationFile.FileName, ChannelName = source.ChannelName,EmailAddress=source.EmailAddress};
            destination.SupportingDocument = new System.Collections.Generic.List<SupportingDocument>();
            destination.HomeOwnerDocument = new System.Collections.Generic.List<SupportingDocument>();
            if (source.SupportingDocument != null)
            {
                foreach (var item in source.SupportingDocument)
                {
                    destination.SupportingDocument.Add(new SupportingDocument() { Name = item.FileName });
                }
            }
            if (source.HomeOwnerDocument != null)
            {
                foreach (var item in source.HomeOwnerDocument)
                {
                    destination.HomeOwnerDocument.Add(new SupportingDocument() { Name = item.FileName });
                }
            }

            return destination;
        }
    }
}
