﻿using AutoMapper;
using EveFileAPI.Domain.Blob;
using Microsoft.AspNetCore.Http;

namespace EveFileAPI.Core.ProfileMapper
{
    public class FormFileTypeConverter : ITypeConverter<IFormFile, BlobData>
    {
        public BlobData Convert(IFormFile source, BlobData destination, ResolutionContext context)
        {  
            
            destination = new BlobData();
            destination.FileName = source.FileName;
            destination.DataAsStream = source.OpenReadStream();
            return destination;
        }
    }
}
