﻿using AutoMapper;
using EveFileAPI.Domain.FileInfo;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EveFileAPI.Core.ProfileMapper
{
    public class StringToEvaluationFileUpdateConverter : ITypeConverter<string, EvaluationFile>
    {
        public EvaluationFile Convert(string source, EvaluationFile destination, ResolutionContext context)
        {
            destination = new EvaluationFile();

            destination.SupportingDocument = new System.Collections.Generic.List<SupportingDocument>();
            destination.HomeOwnerDocument = new System.Collections.Generic.List<SupportingDocument>();

            return destination;
        }
    }
}
