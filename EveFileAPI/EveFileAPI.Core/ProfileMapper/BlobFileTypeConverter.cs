﻿using AutoMapper;
using Azure.Storage.Blobs.Models;
using EveFileAPI.Domain.Blob;

namespace EveFileAPI.Core.ProfileMapper
{
    public class BlobFileTypeConverter : ITypeConverter<BlobDownloadInfo, BlobData>
    {
        public BlobData Convert(BlobDownloadInfo source, BlobData destination, ResolutionContext context)
        {             
            
            destination = new BlobData();
            destination.DataAsStream = source.Content;
            return destination;
        }       
    }
}
