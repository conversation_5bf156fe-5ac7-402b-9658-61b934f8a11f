﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Localization" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.2.5" />
    <PackageReference Include="Microsoft.Azure.ServiceBus" Version="5.2.0" />
    <PackageReference Include="Microsoft.Azure.Services.AppAuthentication" Version="1.6.2" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\EveFileAPI.Common\EveFileAPI.Common.csproj" />
    <ProjectReference Include="..\EveFileAPI.Data\EveFileAPI.Data.csproj" />
    <ProjectReference Include="..\EveFileAPI.Domain\EveFileAPI.Domain.csproj" />
    <ProjectReference Include="..\EveFileAPI.ViewModel\EveFileAPI.ViewModel.csproj" />
  </ItemGroup>
</Project>