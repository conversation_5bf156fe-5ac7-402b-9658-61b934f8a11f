﻿using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using EveFileAPI.Core.DependencyInjection;
using EveFileAPI.ViewModel.Evaluation;

namespace EVEPortal.Services
{

    /// <summary>
    /// Integration Service class for Evaluation API
    /// </summary>
    public class EvaluationLogicAppService : IEvaluationLogicAppService
    {
        private readonly HttpClient client;

        public EvaluationLogicAppService(HttpClient client)
        {
            this.client = client;
        }
        /// <summary>
        /// Get the submission data
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<object> PostFileSubmissionPayload(EvaluationVM request)
        {            
            var response = await client.PostAsJsonAsync(null, request);
            return await response.ReadContentAs<object>();
        }
    }
}
