﻿using EveFileAPI.Core.Services;
using EveFileAPI.Domain.Request;
using Microsoft.Azure.ServiceBus;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace EVEPortal.Services
{
    // Copyright (c) .NET Foundation. Licensed under the Apache License, Version 2.0.
    /// <summary>
    /// Base class for implementing a long running <see cref="IHostedService"/>.
    /// </summary>

    public class ServiceBusEventReceiver : IHostedService
    {
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;
        private QueueClient queueClient;
        private IFileService _fileService;
        public ServiceBusEventReceiver(IConfiguration configuration, ILoggerFactory loggerFactory, IFileService fileService)
        {
            this._logger = loggerFactory.CreateLogger<ServiceBusEventReceiver>();
            this._configuration = configuration;
            _fileService = fileService;
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogDebug($"BusListenerService starting; registering message handler.");
            this.queueClient = new QueueClient(_configuration["EVEFileAPIServiceBusSAS"], _configuration["ServiceBusQueue"]);

            var messageHandlerOptions = new MessageHandlerOptions(e =>
            {
                ProcessError(e.Exception);
                return Task.CompletedTask;
            })
            {
                MaxConcurrentCalls = 1,
                AutoComplete = false
            };
            this.queueClient.RegisterMessageHandler(ProcessMessageAsync, messageHandlerOptions);

            return Task.CompletedTask;
        }

        public async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogDebug($"BusListenerService stopping.");
            await this.queueClient.CloseAsync();
        }

        protected void ProcessError(Exception e)
        {
            _logger.LogError(e, "Error while processing queue item in BusListenerService.");
        }

        protected async Task ProcessMessageAsync(Message message, CancellationToken token)
        {
            var data = Encoding.UTF8.GetString(message.Body);
            ServiceBusEvent<EvaluationReprocessEvent> item = JsonConvert.DeserializeObject<ServiceBusEvent<EvaluationReprocessEvent>>(data);
            _logger.LogInformation("Process the message : ", item);
            await _fileService.Reprocess(item.EventInformation.FileNumber);
            await this.queueClient.CompleteAsync(message.SystemProperties.LockToken);
        }
    }
}

