﻿using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using EveFileAPI.Core.DependencyInjection;
using EveFileAPI.ViewModel;
using EveFileAPI.ViewModel.Evaluation;
using EveFileAPI.ViewModel.HomeOwner;

namespace EVEPortal.Services
{

    /// <summary>
    /// Integration Service class for Evaluation API
    /// </summary>
    public class HomeOwnerXtractService : IHomeOwnerXtractService
    {
        private readonly HttpClient client;

        public HomeOwnerXtractService(HttpClient client)
        {
            this.client = client;
        }
        /// <summary>
        /// Get the submission data
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<HomeOwnerResponse>> PostHomeOwnerInformation(string fileNumber, HomeOwnerPayload request)
        {
            var response = await client.PostAsJsonAsync(fileNumber, request);
            return await response.ReadContentAs<ServiceResponse<HomeOwnerResponse>>();
        }
    }
}
