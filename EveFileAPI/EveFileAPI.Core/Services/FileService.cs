﻿using AutoMapper;
using EveFileAPI.Common.Exceptions;
using EveFileAPI.Common.Extension;
using EveFileAPI.Core.DependencyInjection;
using EveFileAPI.Data.DataLake;
using EveFileAPI.Data.Repository;
using EveFileAPI.Domain.Blob;
using EveFileAPI.Domain.Evaluation;
using EveFileAPI.Domain.FileInfo;
using EveFileAPI.Domain.Request;
using EveFileAPI.ViewModel;
using EveFileAPI.ViewModel.Evaluation;
using EveFileAPI.ViewModel.FileInfo;
using EveFileAPI.ViewModel.HomeOwner;
using EVEPortal.Services;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Reflection.Metadata;
using System.Threading.Tasks;
using System.Xml;

namespace EveFileAPI.Core.Services
{
    public class FileService : IFileService
    {
        private readonly IRepositoryWrapper _repository;
        private readonly IMapper _mapper;
        private readonly IEvaluationDataLakeStorage _storage;
        private readonly IEvaluationH2kfilesDataLakeStorage _h2kfileStorage;
        private readonly IEvaluationArchiveH2kfilesDataLakeStorage _h2kArchivefileStorage;

        private readonly IHomeOwnerXtractService _homeOwner;
        private readonly IEvaluationBulkUploadDataLakeStorage _bulkStorage;
        private readonly HttpClient _client;
        private readonly IHttpContextAccessor _httpContext;
        private readonly IEvaluationLogicAppService _service;

        /// <summary>
        /// Get the submission data
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<object> GetEvaluationDetail(string fileNumber)
        {
            var response = await _client.GetAsync(fileNumber);
            return await response.ReadContentAs<object>();
        }

        public FileService(IRepositoryWrapper repository, IMapper mapper, IEvaluationDataLakeStorage storage, IEvaluationH2kfilesDataLakeStorage h2kfileStorage, IEvaluationArchiveH2kfilesDataLakeStorage h2kArchivefileStorage, IHomeOwnerXtractService homeOwner, IEvaluationBulkUploadDataLakeStorage bulkStorage, HttpClient client, IHttpContextAccessor httpContext,IEvaluationLogicAppService service)
        {
            _repository = repository;
            _mapper = mapper;
            _storage = storage;
            _h2kfileStorage = h2kfileStorage;
            _h2kArchivefileStorage = h2kArchivefileStorage;
            _homeOwner = homeOwner;
            _bulkStorage = bulkStorage;
            _client = client;
            _httpContext = httpContext;
            _service = service;


        }
        public async Task Reprocess(string fileNumber)
        {
            var result = await _repository.Evaluation.GetEvaluationByIdentifierAsync(fileNumber);

            if (result == null)
                return;

            if (result.Evaluationstatus != "Rejected")
                return;

            var fileDetail = await _repository.EvaluationBatch.GetEvaluationBatchByIdAsync(Convert.ToInt32(result.BatchId));
            

            var fileInfo = _mapper.Map<EveFileAPI.Domain.FileInfo.EvaluationFile>(result.Filenumber);
            EvaluationFileBatch batchInfo;
            var fileData = await GetFile(result.Filenumber, Convert.ToInt32(result.BatchId));
            //Restore old files.
            var oldH2kFile = await _repository.EvaluationFileBatch.GetEvaluationByIdentifierAsync(Convert.ToInt32(result.BatchId), result.Filenumber);
            List<VEvaluationSupportingDocumentBatch> oldSupportingDocuments = await _repository.EvaluationSupportingDocumentBatch.GetSupportingDocumentsAsync(oldH2kFile.EvaluationFileBatchId);
            Dictionary<string, string> existingSupportingDocuments = FindDuplicates(result.Filenumber, oldSupportingDocuments.Where(o => o.DocumentType == "SupportingDocument").ToList(), null, fileInfo.SupportingDocument);
            Dictionary<string, string> existingHoReports = FindDuplicates(result.Filenumber, oldSupportingDocuments.Where(o => o.DocumentType == "HOReport").ToList(), null, fileInfo.HomeOwnerDocument);
            //Get directory name
            fileInfo.Name = result.Filenumber + ".H2K";
            fileInfo.SubmissionType = fileDetail.SubmissionType;
            fileInfo.ChannelName = fileDetail.SubmissionChannel;
            batchInfo = _repository.FileStorage.CreateAsync(fileInfo);
            //Upload h2k file
            await _storage.Add(new BlobData() { DataAsStream = fileData, FileName = batchInfo.ADLLandingDirectoryName + batchInfo.EvaluationFileName });
            await CopySupportingDocument(existingSupportingDocuments, batchInfo.SupportingDocumentDirectory, null);
            await CopySupportingDocument(existingHoReports, batchInfo.HomeOwnerReportDirectory, null);
            var payload = _mapper.Map<EvaluationVM>(batchInfo);
            payload.SubmissionType = fileDetail.SubmissionType;
            payload.ChannelName = fileDetail.SubmissionChannel;
            payload.AutoTrigger = true;
            var output = await _service.PostFileSubmissionPayload(payload);            

        }
        public async Task<EvaluationFileBatch> AddEsnhEvaluation(EvaluationFileVM file)
        {

            var filename = file.EvaluationFile.FileName.Split(".");
            var result = await _repository.Evaluation.GetEvaluationByIdentifierAsync(filename[0]);

            EvaluationFileBatch batchInfo=null;
            var fileData = file.EvaluationFile.OpenReadStream();
            var fileInfo = _mapper.Map<EvaluationFile>(file);
            batchInfo = _repository.FileStorage.CreateAsync(fileInfo);
            await _h2kfileStorage.Add(new BlobData() { DataAsStream = fileData, FileName = batchInfo.ADLDirectoryName + batchInfo.EvaluationFileName });
            fileData.Position = 0;
            await _h2kArchivefileStorage.Add(new BlobData() { DataAsStream = fileData, FileName = batchInfo.ADLArchiveDirectoryName + batchInfo.EvaluationFileName });
            return batchInfo;
        }
        public async Task<EvaluationFileBatch> Add(EvaluationFileVM file)
        {

            var filename = file.EvaluationFile.FileName.Split(".");           
            var result = await _repository.Evaluation.GetEvaluationByIdentifierAsync(filename[0]);          

            EvaluationFileBatch batchInfo;
            var fileData = file.EvaluationFile.OpenReadStream();
            var fileInfo = _mapper.Map<EvaluationFile>(file);
            if (result == null)
            {
                batchInfo = _repository.FileStorage.CreateAsync(fileInfo);
                await _storage.Add(new BlobData() { DataAsStream = fileData, FileName = batchInfo.ADLLandingDirectoryName + batchInfo.EvaluationFileName });

                if (file.SupportingDocument != null)
                {
                    foreach (var item in file.SupportingDocument)
                        await _storage.Add(new BlobData() { DataAsStream = item.OpenReadStream(), FileName = batchInfo.SupportingDocumentDirectory + item.FileName });
                }
                if (file.HomeOwnerDocument != null)
                {
                    foreach (var item in file.HomeOwnerDocument)
                        await _storage.Add(new BlobData() { DataAsStream = item.OpenReadStream(), FileName = batchInfo.HomeOwnerReportDirectory + item.FileName });
                }              
            }
            else
            {
                //Restore old files.
                var oldH2kFile = await _repository.EvaluationFileBatch.GetEvaluationByIdentifierAsync(Convert.ToInt32(result.BatchId), result.Filenumber);
                List<VEvaluationSupportingDocumentBatch> oldSupportingDocuments = await _repository.EvaluationSupportingDocumentBatch.GetSupportingDocumentsAsync(oldH2kFile.EvaluationFileBatchId);
                Dictionary<string, string> existingSupportingDocuments = FindDuplicates(result.Filenumber, oldSupportingDocuments.Where(o => o.DocumentType == "SupportingDocument").ToList(), file.SupportingDocument, fileInfo.SupportingDocument);
                Dictionary<string, string> existingHoReports = FindDuplicates(result.Filenumber, oldSupportingDocuments.Where(o => o.DocumentType == "HOReport").ToList(), file.HomeOwnerDocument, fileInfo.HomeOwnerDocument);
                //Get directory name
                batchInfo = _repository.FileStorage.CreateAsync(fileInfo);
                //Upload h2k file
                await _storage.Add(new BlobData() { DataAsStream = fileData, FileName = batchInfo.ADLLandingDirectoryName + batchInfo.EvaluationFileName });
                await CopySupportingDocument(existingSupportingDocuments, batchInfo.SupportingDocumentDirectory, file.SupportingDocument);
                await CopySupportingDocument(existingHoReports, batchInfo.HomeOwnerReportDirectory, file.HomeOwnerDocument);
            }
            //Add Homeowner if it does not exist
            //await PostHomeOwnerAsync(filename[0], file.EvaluationFile, batchInfo, file.ChannelName);

            return batchInfo;
        }
        public async Task<EvaluationBulkFileVM> AddBatch(EvaluationFileVM file)
        {
            var filename = file.EvaluationFile.FileName.Split(".");
            if (filename.Length != 2)
                throw new Exception("Invalid file");
            if (filename[1].ToLower() != "zip")
                throw new Exception("Invalid file");

            var fileInfo = _mapper.Map<EvaluationFile>(file);
            var batchInfo = _repository.FileStorage.CreateBatchAsync(fileInfo);

            var compressedFile = _mapper.Map<BlobData>(file.EvaluationFile);
            compressedFile.FileName = batchInfo.BatchId + "_" + compressedFile.FileName;
            await _bulkStorage.Add(compressedFile);  
            
            return _mapper.Map<EvaluationBulkFileVM>(batchInfo);
        }
        public async Task<EvaluationFileBatch> Update(EvaluationFileUpdateVM file)
        {

            var result = await _repository.Evaluation.GetEvaluationByIdentifierAsync(file.FileNumber);

            var fileInfo = _mapper.Map<EvaluationFile>(file);
            EvaluationFileBatch batchInfo;

            if (file.SubmissionType == "AU" || file.SubmissionType == "MA")
            {
                var currntBatch = await _repository.EvaluationFileBatch.GetEvaluationByIdentifierAsync(Convert.ToInt32(file.BatchId), file.FileNumber);
                batchInfo = _mapper.Map<EvaluationFileBatch>(currntBatch);
                batchInfo.SubmissionType = file.SubmissionType;
                batchInfo.SupportingDocumentDirectory = $"/{file.FileNumber}/{currntBatch.EvaluationFileBatchId}/SupportingDocuments/";
                batchInfo.HomeOwnerReportDirectory = $"/{file.FileNumber}/{currntBatch.EvaluationFileBatchId}/HomeOwnerReports/";

            }
            else if (file.SubmissionType == "MO")
            {
                var currntBatch = await _repository.EvaluationFileBatch.GetEvaluationByIdentifierAsync(Convert.ToInt32(result.BatchId), file.FileNumber);
                batchInfo = _mapper.Map<EvaluationFileBatch>(currntBatch);
                batchInfo.SubmissionType = file.SubmissionType;
                batchInfo.SupportingDocumentDirectory = $"/{file.FileNumber}/{currntBatch.EvaluationFileBatchId}/SupportingDocuments/";
                batchInfo.HomeOwnerReportDirectory = $"/{file.FileNumber}/{currntBatch.EvaluationFileBatchId}/HomeOwnerReports/";

            }
            else if(file.EvaluationFile != null)
            {

                var fileData = file.EvaluationFile.OpenReadStream();
                //Restore old files.
                var oldH2kFile = await _repository.EvaluationFileBatch.GetEvaluationByIdentifierAsync(Convert.ToInt32(result.BatchId), result.Filenumber);
                List<VEvaluationSupportingDocumentBatch> oldSupportingDocuments = await _repository.EvaluationSupportingDocumentBatch.GetSupportingDocumentsAsync(oldH2kFile.EvaluationFileBatchId);
                Dictionary<string, string> existingSupportingDocuments = FindDuplicates(result.Filenumber, oldSupportingDocuments.Where(o => o.DocumentType == "SupportingDocument").ToList(), file.SupportingDocument, fileInfo.SupportingDocument);
                Dictionary<string, string> existingHoReports = FindDuplicates(result.Filenumber, oldSupportingDocuments.Where(o => o.DocumentType == "HOReport").ToList(), file.HomeOwnerDocument, fileInfo.HomeOwnerDocument);                            
                //Get directory name
                batchInfo = _repository.FileStorage.CreateAsync(fileInfo);
                //Upload h2k file
                await _storage.Add(new BlobData() { DataAsStream = fileData, FileName = batchInfo.ADLLandingDirectoryName + batchInfo.EvaluationFileName });
                await CopySupportingDocument(existingSupportingDocuments, batchInfo.SupportingDocumentDirectory, file.SupportingDocument);
                await CopySupportingDocument(existingHoReports, batchInfo.HomeOwnerReportDirectory, file.HomeOwnerDocument);

            }
            else
            {
                batchInfo = _repository.FileStorage.UpdateSupportingDoumentAsync(result.Filenumber, Convert.ToInt32(result.BatchId), fileInfo);
                await CopySupportingDocument(batchInfo.HomeOwnerReportDirectory, file.HomeOwnerDocument);
                await CopySupportingDocument(batchInfo.SupportingDocumentDirectory, file.SupportingDocument);               
            }
            //Add Homeowner if it does not exist
            //await PostHomeOwnerAsync(file.FileNumber, file.EvaluationFile,  batchInfo, file.ChannelName);
            
            return batchInfo;
        }
        public async Task<byte[]> Get(EvaluationFileRequestVM file)
        {

            var result = await _repository.Evaluation.GetEvaluationBySearch(file);
            if (result == null)
                throw new Exception("The file does not exist in System");

            int ruleCount = 0;
            foreach (var item in result)
            {   //ClientName check removed as a result of 41452
				// Trim both name in repo and name in file before comparing
				//var clientName = (item.Clientname)?.Trim();
				//var fullNameHomeOwner = (file.FullNameHomeOwner)?.Trim();
                //if (clientName.Equals(fullNameHomeOwner, StringComparison.InvariantCultureIgnoreCase))
                //    ruleCount++;
                //if (item.Clientaddr.Equals(file.StreetAddress, StringComparison.InvariantCultureIgnoreCase))
                //    ruleCount++;
                //if (item.Clientcity.Equals(file.City, StringComparison.InvariantCultureIgnoreCase))
                //    ruleCount++;
                //if (item.Province.Equals(file.Province, StringComparison.InvariantCultureIgnoreCase))
                //    ruleCount++;
               
                //Do not validate postal code for ESNH files
                if (item.SourceApplication == Constants.Channel_ESNH)
                    ruleCount = 1;
                else
                {
                    if (item.Clientpcode.Replace(" ", "").Equals(file.PostalCode.Replace(" ", ""), StringComparison.InvariantCultureIgnoreCase))
                        ruleCount++;
                }

                if (ruleCount == 1)
                {  //Get the file 
                    Dictionary<string, Stream> files = new Dictionary<string, Stream>();
                    var filePath = await _repository.EvaluationFileBatch.GetEvaluationByIdentifierAsync(Convert.ToInt32(item.BatchId), file.FileNumber);
                    //Add H2K file
                    if (file.DocumentType== "All" || file.DocumentType == "H2K")
                    {
                        var fileData = await _h2kfileStorage.DownloadH2KFile($"{filePath.AdldirectoryName}{filePath.EvaluationFileName}");
                        files.Add(filePath.EvaluationFileName, fileData.DataAsStream);
                    }
                    //return fileData.DataAsStream.ToByteArray();
                    var supportingDocuments = await _repository.EvaluationSupportingDocumentBatch.GetSupportingDocumentsAsync(filePath.EvaluationFileBatchId);                    
                    BlobData supportingDocData;
                    foreach (var supportingDocument in supportingDocuments)
                    {
                        if (file.DocumentType == "All"  || file.DocumentType == supportingDocument.DocumentType)
                        {
                            supportingDocData = await _h2kfileStorage.DownloadH2KFile($"{supportingDocument.AdldirectoryName}{supportingDocument.SupportingDocumentFileName}");
                            files.Add($"{supportingDocument.DocumentType}\\{supportingDocument.SupportingDocumentFileName}", supportingDocData.DataAsStream);
                        }                        
                    }
                    var log = new VEvaluationRequestLog()
                    {
                        EvaluationFileBatchId = filePath.EvaluationFileBatchId,
                        RequestorEmail=file.RequestorEmail
                    };
                    _repository.EvaluationRequestLog.CreateAsync(log);
                    await _repository.SaveAsync();
                    return files.ToZipFile();
                }
                if (ruleCount != 2)
                {  //Get the file 
                    throw new Exception("Invalid postal code or homeowner name");
                }
                break;
            }
            throw new Exception("The file does not exist in System");
        }
       
    
        private async Task PostHomeOwnerAsync(string fileNumber,IFormFile file, EvaluationFileBatch batchInfo, string channelName)
        {
            try
            {

                if (file != null)
                {
                    var test1 = file.OpenReadStream().ToStringExtension();
                    XmlDocument xml = new XmlDocument();
                    xml.LoadXml(test1);

                    if (!(xml.SelectNodes("//HouseFile/Program/Results/Tsv").Count > 0))                    
                        throw new TSVFieldNotFoundException(_httpContext);                        
                    

                    XmlNode client2 = xml.SelectSingleNode("//HouseFile/Program/Results/Tsv/HomeownerAuthorizationID");
                    if (string.IsNullOrEmpty(client2.Attributes[0].InnerText))
                    {
                        //await _storage.Add(new BlobData() { DataAsStream = file.OpenReadStream(), FileName = batchInfo.SupportingDocumentDirectory + "original_" + file.FileName });
                        //EvaluationFile homeFileBatch = new EvaluationFile()
                        //{
                        //    SubmissionType = "MO",
                        //    SupportingDocument = new List<SupportingDocument>() { new SupportingDocument() { Name = "original_" + file.FileName } },
                        //    HomeOwnerDocument = new List<SupportingDocument>()

                        //};

                        //var batchInfo2 = _repository.FileStorage.UpdateSupportingDoumentAsync(fileNumber, batchInfo.BatchId, homeFileBatch);

                        var obj = await _homeOwner.PostHomeOwnerInformation(fileNumber, new HomeOwnerPayload
                        {
                            ADLLandingContainerName = batchInfo.ADLLandingContainerName,
                            ADLLandingDirectoryName = batchInfo.ADLLandingDirectoryName,
                            ChannelName = channelName,
                            EvaluationFileName = batchInfo.EvaluationFileName,
                            EvaluationFileBatchId = batchInfo.BatchId,
                            SupportingDocumentDirectory= batchInfo.SupportingDocumentDirectory
                        });
                    }
                }
            }
            catch(Exception ex)
            {
                throw ex; 
            }
        }

        private Dictionary<string, string> FindDuplicates(string fileNubmer,List<VEvaluationSupportingDocumentBatch> docs, List<IFormFile> files, List<SupportingDocument> supportingDocuments)
        {
            Dictionary<string, string> downloadfiles = new Dictionary<string, string>();

            //Take supporting document from user if it has similar name in old list
            foreach (var item in docs)
            {
                //Dont copy the h2k file if it is added into supporting document folder
                if (item.SupportingDocumentFileName.ToLower().Equals(("original_" + fileNubmer + ".h2k").ToLower(), StringComparison.InvariantCultureIgnoreCase))
                    continue;
                //Supporting Document
                bool flag = true;
                if (files != null && files.Count > 0)
                {
                    foreach (var supDoc in files)
                    {
                        if (supDoc.FileName.ToLower().Equals(item.SupportingDocumentFileName.ToLower()))
                        {
                            flag = false;
                            break;
                        }
                    }
                }
                if (flag)
                {
                    supportingDocuments.Add(new SupportingDocument() { Name = item.ReceivedSupportingDocumentFileName });
                    downloadfiles.Add($"{item.SupportingDocumentFileName}", $"{item.AdldirectoryName}{item.SupportingDocumentFileName}");
                }               
            }
            return downloadfiles;
        }

        private async Task CopySupportingDocument(Dictionary<string, string> existingFiles,string folder, List<IFormFile> currentFiles)
        {
            //Store Uploaded supporting document by user
            if (currentFiles != null && currentFiles.Count > 0)
            {
                foreach (var item in currentFiles)
                    await _storage.Add(new BlobData() { DataAsStream = item.OpenReadStream(), FileName = folder + item.FileName });
            }          

            //Move Old supporting documents to new folder 
            foreach (var down in existingFiles)
            {
                BlobData supportingDocData = await _h2kfileStorage.DownloadH2KFile(down.Value);
                await _storage.Add(new BlobData() { DataAsStream = supportingDocData.DataAsStream, FileName = folder + down.Key });
            }
        }
        private async Task CopySupportingDocument(string directoryName, List<IFormFile> currentFiles)
        {
            if (currentFiles != null && currentFiles.Count > 0)
            {
                foreach (var item in currentFiles)
                {
                    var blob = new BlobData() { DataAsStream = item.OpenReadStream(), FileName = directoryName + item.FileName };
                    await _h2kfileStorage.Delete(blob);
                    await _h2kfileStorage.Add(blob);
                }
            }
        }
        public async Task<Stream> GetFile(string fileNumber, int batchId)
        {
            var filePath = await _repository.EvaluationFileBatch.GetEvaluationByIdentifierAsync(batchId, fileNumber);
            var fileData = await _h2kfileStorage.DownloadH2KFile($"{filePath.AdldirectoryName}{filePath.EvaluationFileName}");
            return fileData.DataAsStream;
        }
    }
}
