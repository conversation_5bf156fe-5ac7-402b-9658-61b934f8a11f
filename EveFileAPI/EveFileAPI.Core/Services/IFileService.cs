﻿using EveFileAPI.Domain.FileInfo;
using EveFileAPI.Domain.Request;
using EveFileAPI.ViewModel.FileInfo;
using System.IO;
using System.Threading.Tasks;

namespace EveFileAPI.Core.Services
{
    public interface IFileService
    {
        Task<EvaluationFileBatch> Add(EvaluationFileVM file);
        Task<EvaluationFileBatch> AddEsnhEvaluation(EvaluationFileVM file);
        Task<EvaluationBulkFileVM> AddBatch(EvaluationFileVM file);
        Task<EvaluationFileBatch> Update(EvaluationFileUpdateVM file);
        Task Reprocess(string fileNumber);
        Task<byte[]> Get(EvaluationFileRequestVM file);
        Task<object> GetEvaluationDetail(string fileNumber);
        Task<Stream> GetFile(string fileNumber, int batchId);

    }
}