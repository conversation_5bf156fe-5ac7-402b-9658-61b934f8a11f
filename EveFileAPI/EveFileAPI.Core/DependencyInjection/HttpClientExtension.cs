﻿using EveFileAPI.Core.Services;
using EVEPortal.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using System;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Threading.Tasks;
using System.Web;

namespace EveFileAPI.Core.DependencyInjection
{
    /// <summary>
    /// Configure the API instances
    /// </summary>
    public static class HttpClientExtension
    {
        public static IServiceCollection AddApiService(this IServiceCollection services, IConfiguration config)
        {
            services.AddHttpContextAccessor();
            services.AddHttpClient<IEvaluationLogicAppService, EvaluationLogicAppService>(
               (services, client) =>
               {
                   var httpRequest = services.GetRequiredService<IHttpContextAccessor>().HttpContext;

                   client.Timeout = TimeSpan.FromSeconds(180);
                   client.BaseAddress = new Uri(config["API:EvaluationValidationLogicApp"]);
                   client.DefaultRequestHeaders.Add("Language", httpRequest!=null?httpRequest.Request.Headers["Language"].ToString():"EN");
               });
            services.AddHttpClient<IHomeOwnerXtractService, HomeOwnerXtractService>(
              (services, client) =>
              {
                  client.BaseAddress = new Uri(config["API:HomeOwnerAPI"]);
                  client.DefaultRequestHeaders.Add("x-functions-key", config[config["API:HomeOwnerAPIKey"].ToString()].ToString());
              });
            services.AddHttpClient<IFileService, FileService>(
              (services, client) =>
              {
                  client.BaseAddress = new Uri(config["API:EvaluationsApi"]);
                  client.DefaultRequestHeaders.Add("Ocp-Apim-Subscription-Key", config["EvaluationsApiKey"].ToString());
              });

            return services;
        }
        public static Task<HttpResponseMessage> PostAsJsonAsync<T>(this HttpClient httpClient, string url, T data)
        {
            var dataAsString = JsonSerializer.Serialize(data);
            var content = new StringContent(dataAsString);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");           
            return httpClient.PostAsync(url, content);
        }

        public static Task<HttpResponseMessage> PutAsJsonAsync<T>(this HttpClient httpClient, string url, T data)
        {
            var dataAsString = JsonSerializer.Serialize(data);
            var content = new StringContent(dataAsString);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");          
            return httpClient.PutAsync(url, content);
        }

        public static async Task<HttpResponseMessage> GetAsync<T>(this HttpClient httpClient, string url, T data)
        {

            var properties = from p in data.GetType().GetProperties()
                             where p.GetValue(data, null) != null
                             select p.Name
                             + "="
                             + HttpUtility.UrlEncode(p.GetValue(data, null).ToString());
            string queryString = String.Join("&", properties.ToArray());
            return await httpClient.GetAsync($"{url}?{queryString}");
        }

        public static async Task<T> ReadContentAs<T>(this HttpResponseMessage response)
        {
            var dataAsString = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

            return JsonSerializer.Deserialize<T>(dataAsString, new JsonSerializerOptions { PropertyNameCaseInsensitive = true, IgnoreNullValues = true });
        }
        public static async Task<FileContentResult> ReadContentAsFile(this HttpResponseMessage response)
        {
            return new FileContentResult(await response.Content.ReadAsByteArrayAsync(), response.Content.Headers.ContentType.MediaType);
        }
    }
}
