﻿using EveFileAPI.Data.DataLake;
using Microsoft.Extensions.Azure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace EveFileAPI.Core.DependencyInjection
{
    public static class BlobStorageExtension
    {
        public static IServiceCollection AddBlobStorage(this IServiceCollection services, IConfiguration config)
        {
            services.AddAzureClients(builder =>
            {
                builder.AddBlobServiceClient(config["StorageDataLake"]).WithName("StorageAccount");
                builder.AddBlobServiceClient(config["StorageDataLake"]).WithName("h2kfiles");
                builder.AddBlobServiceClient(config["StorageDataLake"]).WithName("h2kfilearchive");

            });
            services.TryAddSingleton<IEvaluationDataLakeStorage, EvaluationDataLakeStorage>();
            services.TryAddSingleton<IEvaluationH2kfilesDataLakeStorage, EvaluationH2kfilesDataLakeStorage>();
            services.TryAddSingleton<IEvaluationBulkUploadDataLakeStorage, EvaluationBulkDataLakeStorage>();
            services.TryAddSingleton<IEvaluationArchiveH2kfilesDataLakeStorage, EvaluationArchiveH2kfilesDataLakeStorage>();

            return services;
        }
    }
}
