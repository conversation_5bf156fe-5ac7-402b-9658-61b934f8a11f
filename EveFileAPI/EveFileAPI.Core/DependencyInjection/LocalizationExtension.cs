﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Localization;
using Microsoft.Extensions.DependencyInjection;
using System.Globalization;

namespace EveFileAPI.Core.DependencyInjection
{
    /// <summary>
    /// Configure the API instances
    /// </summary>
    public static class LocalizationExtension
    {
        public static IServiceCollection AddNrcanLocalization(this IServiceCollection services)
        {
            const string enUSCulture = "en-US";

            services.Configure<RequestLocalizationOptions>(options =>
            {
                var supportedCultures = new[]
                {
                new CultureInfo(enUSCulture),
                new CultureInfo("fr-CA")
                };

                options.DefaultRequestCulture = new RequestCulture(culture: enUSCulture, uiCulture: enUSCulture);
                options.SupportedCultures = supportedCultures;
                options.SupportedUICultures = supportedCultures;
                options.RequestCultureProviders.Clear();

                options.RequestCultureProviders.Add(new EveFileAPI.Core.Provider.CustomRequestCultureProvider());

            });

            

            return services;
        }
    }
}
