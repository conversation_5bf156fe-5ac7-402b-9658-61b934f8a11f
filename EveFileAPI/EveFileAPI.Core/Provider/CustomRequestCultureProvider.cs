﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Localization;
using System.Threading.Tasks;

namespace EveFileAPI.Core.Provider
{
    public class CustomRequestCultureProvider : RequestCultureProvider
    {
        public override async Task<ProviderCultureResult> DetermineProviderCultureResult(HttpContext httpContext)
        {
            await Task.Yield();
            return new ProviderCultureResult(httpContext.Request.Headers["Language"].ToString().ToLower() == "fr" ? "fr-CA" : "en-US");
        }
    }
}
