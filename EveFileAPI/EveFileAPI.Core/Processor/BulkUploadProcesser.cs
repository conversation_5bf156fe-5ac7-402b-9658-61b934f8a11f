﻿using EveFileAPI.Core.Services;
using EveFileAPI.ViewModel;
using EveFileAPI.ViewModel.FileInfo;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace EveFileAPI.Core.Processor
{
    public class BulkUploadProcesser : IH2kFileProcesser
    {
        private readonly IFileService _fileService;

        public BulkUploadProcesser(IFileService fileService)
        {
            _fileService = fileService;

        }
        public async Task<ObjectResult> Process(EvaluationFileVM file)
        {
            var batch = await _fileService.AddBatch(file);
            var batchInfo = new ServiceResponse<EvaluationBulkFileVM>();
            batchInfo.Success = true;
            batchInfo.Data = batch;
            return new ObjectResult(batchInfo)
            {
                StatusCode = StatusCodes.Status201Created
            };
        }
    }
}
