﻿using EveFileAPI.Core.Services;
using EveFileAPI.Data.Repository;
using EveFileAPI.ViewModel;
using EveFileAPI.ViewModel.FileInfo;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using EveFileAPI.Common.Extension;
using System;
using System.Text.RegularExpressions;

namespace EveFileAPI.Core.Processor
{
    public class EsnhUploadProcesser : IH2kFileProcesser
    {
        private readonly IFileService _fileService;
        private readonly IRepositoryWrapper _repository;

        public EsnhUploadProcesser(IFileService fileService, IRepositoryWrapper repository)
        {
            _fileService = fileService;
            _repository = repository;

        }
        public async Task<ObjectResult> Process(EvaluationFileVM file)
        {
            
            var xml = file.EvaluationFile.GetXmlDocument();
            var clientName = string.Empty;

            try
            {
                clientName = xml.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/ClientName");
            }
            catch (Exception ex)
            {
                throw new Exception("//HouseFile/Program/Results/Tsv/ClientName element not found", ex.InnerException);
            }
            var clientPostalCode = string.Empty;

            try
            {
                clientPostalCode = xml.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/ClientPcode");
            }
            catch (Exception ex)
            {
                throw new Exception("//HouseFile/Program/Results/Tsv/ClientPcode element not found", ex.InnerException);
            }

            var houseRegion = string.Empty;

            try
            {
                houseRegion = xml.GetXmlTsvValue("//HouseFile/Program/Results/Tsv/HouseRegion");
            }
            catch (Exception ex)
            {
                throw new Exception("//HouseFile/Program/Results/Tsv/HouseRegion element not found", ex.InnerException);
            }

            if (string.IsNullOrWhiteSpace(clientName))
            {
                throw new Exception("Invalid client name!");
            }

            if (string.IsNullOrWhiteSpace(clientPostalCode))
            {
                throw new Exception("Invalid client postal code!");
            }
            if (!ValidateCanadaPostalCode(clientPostalCode))
            {
                throw new Exception("Invalid client postal code!");
            }
            if (string.IsNullOrWhiteSpace(houseRegion))
            {
                throw new Exception("Invalid province!");
            }

            var batch = await _fileService.AddEsnhEvaluation(file);
            var filename = file.EvaluationFile.FileName.Split(".");
            _repository.FileStorage.AddEsnhEvaluation(batch.BatchId, filename[0], clientName, clientPostalCode, houseRegion);
            var batchInfo = new ServiceResponse<bool>();
            batchInfo.Success = true;
            return new ObjectResult(batchInfo)
            {
                StatusCode = StatusCodes.Status201Created
            };
        }
        private bool ValidateCanadaPostalCode(string clientPostalCode)
        {
            string strRegex = @"^[A-Za-z]\d[A-Za-z][ -]?\d[A-Za-z]\d$";
            Regex re = new Regex(strRegex);
            return re.Match(clientPostalCode.ToUpper()).Success;
        }
    }
}

