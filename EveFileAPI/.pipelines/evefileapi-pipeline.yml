# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code.
#
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

variables:
  - name: version.revision
    value: $[counter(format('{0:yyyyMMdd}', pipeline.starttime), 0)]
  - name: version.major
    value: 1
  - name: version.minor
    value: 0
  - name: BuildPlatform
    value: 'Any CPU'
  - name: BuildConfiguration
    value: 'Release'
  - name: branchName
    value: $(Build.SourceBranchName)
  - group: EveFileAPI-dev

name: '$(Build.DefinitionName) $(version.major).$(version.minor).$(Date:yy)$(DayOfYear).$(version.revision)'

trigger:
- dev
- test
- main
- stg

stages:
  - stage: build
    displayName: build
    dependsOn: []
    jobs:
      - job: build
        displayName: 'build the EVE FILE API project artifacts'

        pool:
          vmImage: ubuntu-latest

        steps:
        - task: DotNetCoreCLI@2
          displayName: Restore
          inputs:
            command: 'restore'
            projects: '**/*.csproj'
            feedsToUse: 'select'
        - task: DotNetCoreCLI@2
          displayName: Build
          inputs:
            command: 'build'
            projects: '**/*.csproj'
            arguments: '--configuration $(buildConfiguration)'

        - task: DotNetCoreCLI@2
          displayName: Test  
          inputs:
            command: 'test'
            projects: '**/*[Tt]ests/*.csproj'
            arguments: '--configuration $(buildConfiguration)'    
        - task: DotNetCoreCLI@2
          displayName: Publish
          inputs:
            command: 'publish'
            publishWebProjects: false
            arguments: '--configuration $(buildConfiguration) --output $(build.artifactstagingdirectory)'
        - task: PublishBuildArtifacts@1
          displayName: 'Publish Artifact'
          inputs:
            PathtoPublish: '$(Build.ArtifactStagingDirectory)'
            ArtifactName: 'EveFileAPI'
            publishLocation: 'Container'

  - template: ../.templates/environment-stage.yml
    parameters: 
      stageName: 'dev'
      environmentName: 'EveFileAPI-dev'
      variableGroup: 'EveFileAPI-dev'
      stageDependsOn: [build]
      stageCondition: eq(variables['build.sourceBranchName'], 'dev')

  - template: ../.templates/environment-stage.yml
    parameters: 
      stageName: 'test'
      environmentName: 'EveFileAPI-test'
      variableGroup: 'EveFileAPI-test'
      stageDependsOn: [build]
      stageCondition: eq(variables['build.sourceBranchName'], 'test')

  - template: ../.templates/environment-stage.yml
    parameters: 
      stageName: 'stg'
      environmentName: 'EveFileAPI-stg'
      variableGroup: 'EveFileAPI-stg'
      stageDependsOn: [build]
      stageCondition: eq(variables['build.sourceBranchName'], 'stg')

  - template: ../.templates/environment-stage.yml
    parameters: 
      stageName: 'prod'
      environmentName: 'EveFileAPI-prod'
      variableGroup: 'EveFileAPI-prod'
      stageDependsOn: [build]
      stageCondition: eq(variables['build.sourceBranchName'], 'main')