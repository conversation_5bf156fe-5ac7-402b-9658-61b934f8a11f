{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ConnectionStrings": {"FileApiContext": "Server=sqlsvr-nrcan-oee-hd-prod.database.windows.net,1433;Initial Catalog=sqldb-nrcan-oee-hd-prod;Authentication=Active Directory Interactive;UID=a;Connection Timeout=60;"}, "Storage": {"DataLake": "BlobEndpoint=https://sanrcanoeedlprod.blob.core.windows.net/;QueueEndpoint=https://sanrcanoeedlprod.queue.core.windows.net/;FileEndpoint=https://sanrcanoeedlprod.file.core.windows.net/;TableEndpoint=https://sanrcanoeedlprod.table.core.windows.net/;SharedAccessSignature=sv=2020-08-04&ss=bfqt&srt=sco&sp=rwdlacupx&se=2024-07-29T02:38:43Z&st=2021-07-27T18:38:43Z&spr=https&sig=ioyDYVdwP3DJJR6CuASchj%2FQq5LQGUuBW5C0e8qz5t0%3D"}, "API": {"EvaluationValidationLogicApp": "https://prod-09.canadacentral.logic.azure.com:443/workflows/d0d753e7ccc9405e8eeb79e0f66268ad/triggers/manual/paths/invoke?api-version=2016-10-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=ImpCANl0MOpJ_3MhhMrcAFERE_uE0m3ARPPXB7PI9tY", "HomeOwnerAPI": "https://homeowner-nrcan-oee-hd-prod.azurewebsites.net/api/HomeOwners/", "HomeOwnerAPIKey": "homeowner-nrcan-oee-hd-prod", "EvaluationsApi": "********************************************/evaluations/", "EvaluationsApiKey": "e7c5e4c6219d426a8e8eba28618816fcs"}, "EVEFileAPIServiceBusSAS": "Endpoint=sb://sb-nrcan-oee-evaluation-processing-dev.servicebus.windows.net/;SharedAccessKeyName=ManageSAS;SharedAccessKey=igRjO385vjV2Wt2EhrVbyyrPCq5g7D4iQ2MPRRmJ25c=", "ServiceBusQueue": "queue-nrcan-oee-evaluation-event-prod", "KeyVault": "kv-nrcan-oee-eve-prod"}