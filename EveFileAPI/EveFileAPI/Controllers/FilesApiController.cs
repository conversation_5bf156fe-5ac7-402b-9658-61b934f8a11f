﻿using EveFileAPI.Core.Services;
using EveFileAPI.Domain.FileInfo;
using EveFileAPI.ViewModel;
using EveFileAPI.ViewModel.FileInfo;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
namespace EveFileAPI.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class FilesApiController : ControllerBase
    {

        private readonly ILogger<FilesApiController> _logger;
        private readonly IFileService _service;
        private readonly IHttpContextAccessor _httpContext;

        public FilesApiController(ILogger<FilesApiController> logger, IFileService service, IHttpContextAccessor httpContext)
        {
            _logger = logger;
            _service = service;
            _httpContext = httpContext;
        }
        [HttpPost]
        [ProducesResponseType(typeof(ServiceResponse<EvaluationFileBatch>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [RequestFormLimits(MultipartBodyLengthLimit = 209715200)]
        [RequestSizeLimit(209715200)]
        public async Task<ActionResult<ServiceResponse<EvaluationFileBatch>>> Post([FromForm] EvaluationFileVM file)
        {
            _logger.LogInformation($"Entering to {nameof(Post)}", file);

            try
            {


                ServiceResponse<EvaluationFileBatch> response = new ServiceResponse<EvaluationFileBatch>()
                {
                    Data = await _service.Add(file),
                    Success = true
                };
                return StatusCode(StatusCodes.Status200OK, response);
            }

            catch (Exception ex)
            {

                ServiceResponse<EvaluationFileBatch> response = new ServiceResponse<EvaluationFileBatch>()
                {
                    Message = ex.Message,
                    Success = false
                };
                return StatusCode(StatusCodes.Status400BadRequest, response);
            }

        }

        [HttpPut("{fileNumber}")]
        [ProducesResponseType(typeof(ServiceResponse<EvaluationFileBatch>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [RequestFormLimits(MultipartBodyLengthLimit = 209715200)]
        [RequestSizeLimit(209715200)]
        public async Task<ActionResult<ServiceResponse<EvaluationFileBatch>>> Put(string fileNumber, [FromForm] EvaluationFileUpdateVM file)
        {
            _logger.LogInformation($"Entering to {nameof(Post)}", file);
            try
            {
                if (fileNumber != file.FileNumber)
                {
                    return BadRequest();
                }

                ServiceResponse<EvaluationFileBatch> response = new ServiceResponse<EvaluationFileBatch>()
                {
                    Data = await _service.Update(file),
                    Success = true
                };
                return StatusCode(StatusCodes.Status200OK, response);
            }
            catch (Exception ex)
            {

                ServiceResponse<EvaluationFileBatch> response = new ServiceResponse<EvaluationFileBatch>()
                {
                    Message = ex.Message,
                    Success = false
                };
                _logger.LogError(ex, ex.Message);
                return StatusCode(StatusCodes.Status400BadRequest, response);
            }
        }


    }

}
