﻿using EveFileAPI.Core.Processor;
using EveFileAPI.Core.Services;
using EveFileAPI.Data.Repository;
using EveFileAPI.Domain.FileInfo;
using EveFileAPI.Domain.Request;
using EveFileAPI.ResourceLibrary;
using EveFileAPI.ViewModel;
using EveFileAPI.ViewModel.Evaluation;
using EveFileAPI.ViewModel.FileInfo;
using EVEPortal.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace EveFileAPI.Controllers
{

    [ApiController]
    [Route("Evaluation")]
    public class EvaluationApiController : ControllerBase
    {
        private readonly IRepositoryWrapper _repository;

        private readonly IStringLocalizer<SharedResource> _localizer;
        private readonly ILogger<FilesApiController> _logger;
        private readonly IEvaluationLogicAppService _service;
        private readonly IFileService _fileService;
        private readonly IHttpContextAccessor _httpContext;

        public EvaluationApiController(IRepositoryWrapper repository, ILogger<FilesApiController> logger, IEvaluationLogicAppService service, IFileService fileService, IHttpContextAccessor httpContext,IStringLocalizer<SharedResource> localizer) 
        {
            _repository = repository;

            _logger = logger;
            _service = service;
            _fileService = fileService;
            _httpContext = httpContext;
            _localizer = localizer;

        }
        [HttpPost]
        [RequestFormLimits(MultipartBodyLengthLimit = 209715200)]
        [RequestSizeLimit(209715200)]
        [ProducesResponseType(typeof(ServiceResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async  Task<ActionResult> Post([FromForm] EvaluationFileVM file)
        {

            _logger.LogInformation($"EvaluationApiController - Entering to {nameof(Post)}", file);
            try
            {
                
                //Process the batch file
                if (file.SubmissionType.StartsWith("B", StringComparison.OrdinalIgnoreCase) && file.ChannelName.ToLower().StartsWith(Constants.Channel_B2BApi, StringComparison.OrdinalIgnoreCase))
                {
                    IH2kFileProcesser processor = new BulkUploadProcesser(_fileService);
                    return await processor.Process(file);
                }

                if (file.SubmissionType.StartsWith("B", StringComparison.OrdinalIgnoreCase) && file.ChannelName.ToLower().StartsWith(Constants.Channel_HOPortal, StringComparison.OrdinalIgnoreCase))
                {
                    IH2kFileProcesser processor = new BulkUploadProcesser(_fileService);
                    return await processor.Process(file);
                }
                
                if (file.ChannelName.ToUpper().StartsWith(Constants.Channel_ESNH, StringComparison.OrdinalIgnoreCase))
                {
                    IH2kFileProcesser processor = new EsnhUploadProcesser(_fileService, _repository);
                    return await processor.Process(file);
                }

                //Process the Single File
                FilesApiController ctr = new FilesApiController(_logger, _fileService, _httpContext);
                var fileStore = (EveFileAPI.ViewModel.ServiceResponse<EveFileAPI.Domain.FileInfo.EvaluationFileBatch>)((Microsoft.AspNetCore.Mvc.ObjectResult)(await ctr.Post(file)).Result).Value;

                //Return if file could not be created
                if (!fileStore.Success)
                {
                    var response = new ServiceResponse<String>()
                    {
                        Message = fileStore.Message,
                        Success = false
                    };
                    return StatusCode(StatusCodes.Status400BadRequest, response);
                }

                var payload = new EvaluationVM() {
                    ADLArchiveContainerName = fileStore.Data.ADLArchiveContainerName,
                    ADLArchiveDirectoryName = fileStore.Data.ADLArchiveDirectoryName,
                    ADLContainerName = fileStore.Data.ADLContainerName,
                    ADLDirectoryName = fileStore.Data.ADLDirectoryName,
                    ADLLandingContainerName = fileStore.Data.ADLLandingContainerName,
                    ADLLandingDirectoryName = fileStore.Data.ADLLandingDirectoryName,
                    EvaluationFileBatchId = fileStore.Data.EvaluationFileBatchId,
                    EvaluationFileName = fileStore.Data.EvaluationFileName,
                    ReceivedDirectoryName = fileStore.Data.ReceivedDirectoryName,
                    ReceivedEvaluationFileName = fileStore.Data.ReceivedEvaluationFileName,
                    SupportingDocumentDirectory= fileStore.Data.SupportingDocumentDirectory,
                    HomeOwnerReportDirectory = fileStore.Data.HomeOwnerReportDirectory,
                    SubmissionType = file.SubmissionType,
                    ChannelName=file.ChannelName
                };
                var output = await _service.PostFileSubmissionPayload(payload);

                _logger.LogInformation($"EvaluationApiController - leaving {nameof(Post)} - result : {JsonConvert.SerializeObject(output)}\n");
                return StatusCode(StatusCodes.Status201Created, output); 
                               

               
            }
            catch (Exception ex)            
            {

                var response = new ServiceResponse<String>()
                {
                    Message = ex.Message,
                    Success = false
                };
                _logger.LogError(ex, ex.Message);
                return StatusCode(StatusCodes.Status400BadRequest, response);
            }
        }
        [HttpPut("{fileNumber}")]
        [RequestFormLimits(MultipartBodyLengthLimit = 209715200)]
        [RequestSizeLimit( 209715200)]
        [ProducesResponseType(typeof(ServiceResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public  async Task<ActionResult<ServiceResponse<EvaluationFileBatch>>> Put(string fileNumber, [FromForm] EvaluationFileUpdateVM file)
        {

            _logger.LogInformation($"EvaluationApiController - Entering to {nameof(Put)}", file);
            try
            {                

                FilesApiController ctr = new FilesApiController(_logger, _fileService, _httpContext);
                var fileStore = (EveFileAPI.ViewModel.ServiceResponse<EveFileAPI.Domain.FileInfo.EvaluationFileBatch>)((Microsoft.AspNetCore.Mvc.ObjectResult)(await ctr.Put(fileNumber,file)).Result).Value;

                //Do not call logic app if H2K file is not available
                if (file.EvaluationFile == null && fileStore.Success && file.SubmissionType=="U")
                {
                    ServiceResponse<bool> response = new ServiceResponse<bool>()
                    {
                        Success = true,
                        Data = true
                    };
                    return StatusCode(StatusCodes.Status200OK, response);
                }

                if (fileStore.Success)
                {
                    var payload = new EvaluationVM()
                    {
                        ADLArchiveContainerName = fileStore.Data.ADLArchiveContainerName,
                        ADLArchiveDirectoryName = fileStore.Data.ADLArchiveDirectoryName,
                        ADLContainerName = fileStore.Data.ADLContainerName,
                        ADLDirectoryName = fileStore.Data.ADLDirectoryName,
                        ADLLandingContainerName = fileStore.Data.ADLLandingContainerName,
                        ADLLandingDirectoryName = fileStore.Data.ADLLandingDirectoryName,
                        EvaluationFileBatchId = fileStore.Data.EvaluationFileBatchId,
                        EvaluationFileName = fileStore.Data.EvaluationFileName,
                        ReceivedDirectoryName = fileStore.Data.ReceivedDirectoryName,
                        ReceivedEvaluationFileName = fileStore.Data.ReceivedEvaluationFileName,
                        Comments = file.Comments,
                        SubmissionType = file.SubmissionType,
                        ChannelName = file.ChannelName,
                        SupportingDocumentDirectory = fileStore.Data.SupportingDocumentDirectory,
                        HomeOwnerReportDirectory = fileStore.Data.HomeOwnerReportDirectory,

                    };
                    var output = await _service.PostFileSubmissionPayload(payload);
                    _logger.LogInformation($"EvaluationApiController - leaving {nameof(Put)} - result : {JsonConvert.SerializeObject(output)}\n");
                    return StatusCode(StatusCodes.Status200OK, output);
                }
                _logger.LogInformation($"EvaluationApiController - leaving {nameof(Put)} - Status400BadRequest : {JsonConvert.SerializeObject(fileStore)}\n");
                return StatusCode(StatusCodes.Status400BadRequest, fileStore);
            }
            catch (Exception ex)
            {

                ServiceResponse<String> response = new ServiceResponse<String>()
                {
                    Message = ex.Message,
                    Success = false
                };
                _logger.LogError(ex, ex.Message);
                return StatusCode(StatusCodes.Status400BadRequest, response);
            }
        }
        [HttpGet("{fileNumber}")]        
        [ProducesResponseType(typeof(ActionResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> Get(string fileNumber, [FromQuery] EvaluationFileRequestVM file)
        {
            try
            {                
                _logger.LogInformation($"EvaluationApiController - Entering to {nameof(Get)}", file);
                if (Request.Headers["ResponseType"].ToString() != "File")
                    return   StatusCode(StatusCodes.Status200OK, new ServiceResponse<String>() { Success=false, Message= _localizer["Invalid request type"] });
                if (fileNumber != file.FileNumber)
                {
                    return BadRequest();
                }
                var output = await _fileService.Get(file);
                _logger.LogInformation($"EvaluationApiController - leaving {nameof(Get)} \n");
                // return File(output, $"application/xml", $"{fileNumber}.h2k");
                return File(output, $"application/octet-stream", $"{fileNumber}.zip");           
            }
            
            catch (Exception ex)
            {

                ServiceResponse<string> response = new ServiceResponse<string>()
                {
                    Message = ex.Message,
                    Success = false
                };
                _logger.LogError(ex, ex.Message);
                return StatusCode(StatusCodes.Status400BadRequest, response);
            }
        }
    }
}
