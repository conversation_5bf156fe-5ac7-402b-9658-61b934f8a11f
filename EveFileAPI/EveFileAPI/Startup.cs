using EveFileAPI.Core.DependencyInjection;
using EveFileAPI.Core.ProfileMapper;
using EveFileAPI.Data.Repository;
using EveFileAPI.ViewModel;
using EVEPortal.Services;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using System.Linq;

namespace EveFileAPI
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddLocalization(options => options.ResourcesPath = "Resources");
            services.AddNrcanLocalization();
            
            services.AddDbContext<RepositoryContext>(options =>
                   options.UseSqlServer(Configuration["AzureSQLConnectionStringForAppService"]));
            services.AddControllers().ConfigureApiBehaviorOptions(action =>
            {
                action.InvalidModelStateResponseFactory = actionContext =>
                {
                    return (new BadRequestObjectResult(new ServiceResponse<string>()
                    {
                        Success = false,
                        Message = string.Join("; ", actionContext.ModelState.Values
                                         .SelectMany(x => x.Errors)
                                         .Select(x => x.ErrorMessage))
                    }));
                };
                
            });
            services.AddAutoMapper(typeof(ProfileAutoMapper));
            services.AddScoped<IRepositoryWrapper, RepositoryWrapper>();

            services.AddBlobStorage(Configuration);
            services.AddApiService(Configuration);
            services.AddMvc();
            services.AddHostedService<ServiceBusEventReceiver>();

            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "EveFileAPI", Version = "v1" });
            });
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (!env.IsProduction())
            {
                app.UseDeveloperExceptionPage();
                app.UseSwagger();
                app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "EveFileAPI v1"));
            }

            app.UseHttpsRedirection();            
            app.UseRouting();
            app.UseRequestLocalization();
            app.UseAuthorization();
            app.UseCors();
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
        private BadRequestObjectResult CustomErrorResponse(ActionContext actionContext)
        {
            //BadRequestObjectResult is class found Microsoft.AspNetCore.Mvc and is inherited from ObjectResult.    
            //Rest code is linq.    
            return new BadRequestObjectResult(actionContext.ModelState
             .Where(modelError => modelError.Value.Errors.Count > 0)
             .Select(modelError => new ServiceResponse<string>
             {
                 Success = false,
                 Message = modelError.Value.Errors.FirstOrDefault().ErrorMessage
             }));
        }
    }
}
