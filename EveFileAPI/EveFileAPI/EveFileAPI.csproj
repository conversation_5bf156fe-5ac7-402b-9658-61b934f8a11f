﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <UserSecretsId>feb0e015-ff64-49cd-8a3c-0bcfcd023a97</UserSecretsId>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.2.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.11" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.11">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\EveFileAPI.Core\EveFileAPI.Core.csproj" />
    <ProjectReference Include="..\EveFileAPI.ResourceLibrary\EveFileAPI.ResourceLibrary.csproj" />
    <ProjectReference Include="..\EveFileAPI.ViewModel\EveFileAPI.ViewModel.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Processor\" />
  </ItemGroup>
</Project>