{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "API": {"EvaluationValidationLogicApp": "https://prod-25.canadacentral.logic.azure.com:443/workflows/cbaef115b6834bcb9e1c705a67a8e21d/triggers/manual/paths/invoke?api-version=2016-10-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=pCUNKHlrPFtf3eX0JHfezR9n447MidQVw-Xm4nNJ2fw", "HomeOwnerAPI": "https://homeowner-nrcan-oee-hd-dev.azurewebsites.net/api/HomeOwners/", "HomeOwnerAPIKey": "homeowner-nrcan-oee-hd-dev", "EvaluationsApi": "*******************************************/evaluations/"}, "EVEFileAPIServiceBusSAS": "Endpoint=sb://sb-nrcan-oee-evaluation-processing-dev.servicebus.windows.net/;SharedAccessKeyName=ManageSAS;SharedAccessKey=igRjO385vjV2Wt2EhrVbyyrPCq5g7D4iQ2MPRRmJ25c=", "ServiceBusQueue": "queue-nrcan-oee-evaluation-event-dev", "KeyVault": "kv-nrcan-oee-eve-dev"}