{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ConnectionStrings": {"FileApiContext": "Server=sqlsvr-nrcan-oee-test.database.windows.net,1433;Initial Catalog=sqldb-nrcan-oee-test;Authentication=Active Directory Interactive;UID=a;Connection Timeout=60;"}, "Storage": {"DataLake": "BlobEndpoint=https://sanrcanoeedltest.blob.core.windows.net/;QueueEndpoint=https://sanrcanoeedltest.queue.core.windows.net/;FileEndpoint=https://sanrcanoeedltest.file.core.windows.net/;TableEndpoint=https://sanrcanoeedltest.table.core.windows.net/;SharedAccessSignature=sv=2020-08-04&ss=bfqt&srt=sco&sp=rwdlacupx&se=2024-07-22T11:17:58Z&st=2021-07-21T03:17:58Z&spr=https&sig=YUmInrVCGEB2LJeql%2BJXGCdZBNHOesd9aGd159eMlkw%3D"}, "API": {"EvaluationValidationLogicApp": "https://prod-27.canadacentral.logic.azure.com:443/workflows/87b34c001da84f939a5534ac743313bf/triggers/manual/paths/invoke?api-version=2016-10-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=IIwBmnwmy30ivpEUvItL-ms0WjNRRIVtQdh1zkCXL1Q", "HomeOwnerAPI": "https://homeowner-nrcan-oee-hd-test.azurewebsites.net/api/HomeOwners/", "HomeOwnerAPIKey": "homeowner-nrcan-oee-hd-test", "EvaluationsApi": "*****************************************/evaluations/", "EvaluationsApiKey": "fe611c05fc624455936a9c123b7a6599"}, "EVEFileAPIServiceBusSAS": "Endpoint=sb://sb-nrcan-oee-evaluation-processing-dev.servicebus.windows.net/;SharedAccessKeyName=ManageSAS;SharedAccessKey=igRjO385vjV2Wt2EhrVbyyrPCq5g7D4iQ2MPRRmJ25c=", "ServiceBusQueue": "queue-nrcan-oee-evaluation-event-test", "KeyVault": "kv-nrcan-oee-eve-test"}