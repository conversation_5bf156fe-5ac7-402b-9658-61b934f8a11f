{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ConnectionStrings": {"FileApiContext": "Server=sqlsvr-nrcan-oee-stg.database.windows.net,1433;Initial Catalog=sqldb-nrcan-oee-stg;Authentication=Active Directory Interactive;UID=a;Connection Timeout=180;"}, "Storage": {"DataLake": "BlobEndpoint=https://sanrcanoeedlprod.blob.core.windows.net/;QueueEndpoint=https://sanrcanoeedlprod.queue.core.windows.net/;FileEndpoint=https://sanrcanoeedlprod.file.core.windows.net/;TableEndpoint=https://sanrcanoeedlprod.table.core.windows.net/;SharedAccessSignature=sv=2020-08-04&ss=bfqt&srt=sco&sp=rwdlacupx&se=2024-07-29T02:38:43Z&st=2021-07-27T18:38:43Z&spr=https&sig=ioyDYVdwP3DJJR6CuASchj%2FQq5LQGUuBW5C0e8qz5t0%3D"}, "API": {"EvaluationValidationLogicApp": "https://prod-22.canadacentral.logic.azure.com:443/workflows/0576eae7175a49cfa2a62737a87ffa9f/triggers/manual/paths/invoke?api-version=2016-10-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=-v7axwwO48r4s-Z6IrCaBvkQiJ7C3EBxD1wjkQS1UNI", "HomeOwnerAPI": "https://func-nrcan-oee-homeowner-api-stg.azurewebsites.net/api/HomeOwners/", "HomeOwnerAPIKey": "func-nrcan-oee-homeowner-api-stg", "EvaluationsApi": "*******************************************/evaluations/", "EvaluationsApiKey": "ad36ebc0de8346588290a44af6d87a13"}, "EVEFileAPIServiceBusSAS": "Endpoint=sb://sb-nrcan-oee-stg.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=qd73bFX5uk6PfFH6G3uM+B+Ul/nBgvdaP+ASbHjmdsI=", "ServiceBusQueue": "queue-nrcan-oee-evaluation-event-stg", "KeyVault": "kvnrcanoeeshareds"}