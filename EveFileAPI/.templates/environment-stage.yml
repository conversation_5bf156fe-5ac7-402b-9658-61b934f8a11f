# template for deploying to different verticals

parameters:
- name: stageName
- name: environmentName
- name: variableGroup
  default: 'EveFileAPI-dev'
- name: stageCondition
  type: object
  default: 'succeeded()'
- name: stageDependsOn
  type: object
  default: []


stages:
- stage: ${{ parameters.stageName }}
  condition: ${{ parameters.stageCondition }} 
  dependsOn: ${{ parameters.stageDependsOn }}
  jobs:
  - deployment: AppService
    displayName: 'Deploy to Azure App Service'
    pool:
      vmImage: windows-latest
    variables:
      - group: ${{ parameters.variableGroup }}
    environment: ${{ parameters.environmentName }}
    strategy:
      runOnce:
        deploy:
          steps:
          - download: none
          - script: echo 'Deploying EveFileAPI to $(environment)'
          - task: DownloadPipelineArtifact@2
            displayName: 'Download API Web Package'
            inputs:
              buildType: 'current'
              artifactName: 'EveFileAPI'
              targetPath: '$(Pipeline.workspace)/package'
          - task: AzureRmWebAppDeployment@4
            displayName: 'Azure App Service Deploy: $(WebAppName)'
            inputs:
              azureSubscription: $(AzureSubscription)
              WebAppName: $(WebAppName)
              packageForLinux: '$(Pipeline.workspace)/package/a.zip'