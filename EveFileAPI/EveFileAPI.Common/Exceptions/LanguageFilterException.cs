﻿using Microsoft.AspNetCore.Http;
using System;

namespace EveFileAPI.Common.Exceptions
{
    public abstract class LanguageFilterException : Exception
    {
        private readonly IHttpContextAccessor _httpContext;

        public LanguageFilterException(IHttpContextAccessor httpContext)
        {
            _httpContext = httpContext;

        }
        public virtual bool IsEnglish()
        {
            return !(_httpContext.HttpContext.Request.Headers["Language"].Count > 0 && Convert.ToString(_httpContext.HttpContext.Request.Headers["Language"]).ToLower() == "fr");
        }
    }
}
