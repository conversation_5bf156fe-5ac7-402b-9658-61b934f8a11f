﻿using Microsoft.AspNetCore.Http;

namespace EveFileAPI.Common.Exceptions
{
    public class EvaluationNotExistsException : LanguageFilterException
    {
        public EvaluationNotExistsException(IHttpContextAccessor httpContext) : base(httpContext)
        {

        }
        public override string Message
        {
            get
            {
                if (!IsEnglish())
                {
                    return $"The evaluation does not exist in System.";
                }
                return $"The evaluation does not exist in System.";
            }
        }
    }
}
