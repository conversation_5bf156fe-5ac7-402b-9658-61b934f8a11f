﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EveFileAPI.Common.Exceptions
{
    public class QcOrNsFileRejectException : LanguageFilterException
    {
        public QcOrNsFileRejectException(IHttpContextAccessor httpContext) : base(httpContext)
        {

        }
        public override string Message
        {
            get
            {
                if (!IsEnglish())
                {
                    return $"Les fichiers de maisons situées au Québec ou en Nouvelle-Écosse ne peuvent être soumis au portail de la Subvention canadienne pour des maisons plus vertes. Veuillez soumettre ces fichiers au répondeur automatique de courrier électronique (Mailbot).";
                }
                return $"Files located in Quebec and Nova Scotia cannot be submitted to the Greener Homes Portal. Please submit these files through the MailBot instead.";
            }
        }
    }
}
