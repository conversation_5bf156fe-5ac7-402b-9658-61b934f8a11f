﻿using Microsoft.AspNetCore.Http;

namespace EveFileAPI.Common.Exceptions
{
    public class MailbotFileRejectionException : LanguageFilterException
    {
        public MailbotFileRejectionException(IHttpContextAccessor httpContext) : base(httpContext)
        {

        }
        public override string Message {
            get
            {
                if (!IsEnglish())
                {
                    return $"Ce fichier a été initialement soumis au répondeur automatique de courriel.Si une mise à jour est nécessaire, veuillez l'envoyer au répondeur automatique de courriel.";
                }
                return $"This file was initially submitted to the MailBot. If an update is required, please send it to the MailBot.";
            }
        }
    }
}
