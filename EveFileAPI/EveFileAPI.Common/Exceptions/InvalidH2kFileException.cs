﻿using Microsoft.AspNetCore.Http;

namespace EveFileAPI.Common.Exceptions
{

    public class InvalidH2kFileException : LanguageFilterException
    {
        public InvalidH2kFileException(IHttpContextAccessor httpContext) : base(httpContext)
        {

        }
        public override string Message
        {
            get
            {
                if (!IsEnglish())
                {
                    return $"Invalid file.";
                }
                return $"Invalid file.";
            }
        }
    }
}
