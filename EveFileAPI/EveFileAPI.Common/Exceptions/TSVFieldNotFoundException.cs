﻿using Microsoft.AspNetCore.Http;

namespace EveFileAPI.Common.Exceptions
{

    public class TSVFieldNotFoundException : LanguageFilterException
    {
        public TSVFieldNotFoundException(IHttpContextAccessor httpContext) : base(httpContext)
        {

        }
        public override string Message
        {
            get
            {
                if (!IsEnglish())
                {
                    return $"Aucune pièce jointe valide détectée. Veuillez sélectionner le mode Système de cote ÉnerGuide ou le mode ERS Maison de référence de l’Ontario et soumettre à nouveau votre fichier.";
                }
                return $"No valid attachment detected. Please select either the EnerGuide Rating System or ERS Ontario Reference House mode and re-submit your file.";
            }
        }
    }
}
