﻿using System;
using System.Xml;

namespace EveFileAPI.Common.Helper
{
    public class XMLHelper
    {
        public static string GetXmlTsvValue(string fieldname, XmlDocument h2k, int Attribute = 0)
        {
            string value = null;

            XmlNode ruleNode = h2k.SelectSingleNode(fieldname);
            if (!(ruleNode is null))
            {
                if (Attribute == -1)
                {
                    value = Convert.ToString(ruleNode.InnerText);
                }
                else if (ruleNode != null)
                {
                    value = Convert.ToString(ruleNode.Attributes[Attribute].InnerText);
                }
            }
            return value;

        }
    }
}
