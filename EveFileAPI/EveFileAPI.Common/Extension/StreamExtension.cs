﻿using System.IO;
namespace EveFileAPI.Common.Extension
{
    public static class StreamExtension
    {
        public static byte[] ToByteArray(this Stream stream)
        {          
            using (var memoryStream = new MemoryStream())
            {
                stream.CopyTo(memoryStream);
                return memoryStream.ToArray();
            }
        }
        public static string ToStringExtension(this Stream stream)
        {           
            return new StreamReader(stream).ReadToEnd();            
        }        

    }
}
