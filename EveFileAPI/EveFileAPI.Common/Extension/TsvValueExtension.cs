﻿using Microsoft.AspNetCore.Http;
using System.Xml;

namespace EveFileAPI.Common.Extension
{
    public static class TsvValueExtension
    {
        public static string GetXmlTsvValue(this IFormFile file, string fieldName)
        {
            XmlDocument xml =  file.GetXmlDocument();
            string value = string.Empty;          
            XmlNode ruleNode = xml.SelectSingleNode(fieldName);            
            if (ruleNode != null)
                value = ruleNode.Attributes["value"].InnerText;        
            return value;
        }
        public static string GetXmlTsvValue(this XmlDocument xml, string fieldName)
        {
            string value = string.Empty;
            XmlNode ruleNode = xml.SelectSingleNode(fieldName);
            if (ruleNode != null)
                value = ruleNode.Attributes["value"].InnerText;
            return value;
        }

        public static int GetXmlTsvFieldCount(this IFormFile file, string fieldName)
        {
            int value = 0;
            XmlDocument xml = file.GetXmlDocument();
            XmlNodeList nodeList = xml.SelectNodes(fieldName);
            if (nodeList != null)
                value = nodeList.Count;
            return value;
        }
        public static XmlDocument GetXmlDocument(this IFormFile file)
        {
            if (file != null)
            {
                XmlDocument xml = new XmlDocument();
                xml.LoadXml(file.OpenReadStream().ToStringExtension());
                return xml;
            }
            return null;
        }
    }
}
