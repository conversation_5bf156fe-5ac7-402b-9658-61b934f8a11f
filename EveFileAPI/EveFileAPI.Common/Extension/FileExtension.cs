﻿using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EveFileAPI.Common.Extension
{
    public static class FileExtension
    {
        public static byte[] ToZipFile(this Dictionary<string,Stream> files)
        {
            using (var ms = new MemoryStream())
            {
                using (var zipArchive = new ZipArchive(ms, ZipArchiveMode.Create, true))
                {
                    foreach (var attachment in files)
                    {                        
                        var entry = zipArchive.CreateEntry(attachment.Key, CompressionLevel.Fastest);
                        using (var entryStream = entry.Open())
                        {
                            attachment.Value.CopyTo(entryStream);
                        }
                    }
                }
                return ms.ToArray();
            }
        }
        public static Stream ToZipFileStream(this Dictionary<string, Stream> files)
        {
            using (var ms = new MemoryStream())
            {
                using (var zipArchive = new ZipArchive(ms, ZipArchiveMode.Create, true))
                {
                    foreach (var attachment in files)
                    {
                        var entry = zipArchive.CreateEntry(attachment.Key, CompressionLevel.Fastest);
                        using (var entryStream = entry.Open())
                        {
                            attachment.Value.CopyTo(entryStream);
                        }
                    }
                }
                return ms;
            }
        }
    }
}
