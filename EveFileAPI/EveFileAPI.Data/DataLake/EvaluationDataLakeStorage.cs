﻿using AutoMapper;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using EveFileAPI.Domain.Blob;
using Microsoft.Extensions.Azure;
using System.Threading.Tasks;

namespace EveFileAPI.Data.DataLake
{

    public class EvaluationDataLakeStorage : IEvaluationDataLakeStorage
    {

        private readonly BlobContainerClient _container;
        private readonly IMapper _mapper;
       
        public EvaluationDataLakeStorage(IAzureClientFactory<BlobServiceClient> clientFactory, IMapper mapper)
        {
            BlobServiceClient client = clientFactory.CreateClient("StorageAccount");
            _container = client.GetBlobContainerClient("h2kfilelanding");
            _mapper = mapper;
        }

        public async Task<bool> Add(BlobData data)
        {
            await _container.UploadBlobAsync(data.FileName, data.DataAsStream);
            return true;
        }
        public async Task<bool> FileExists(BlobData data)
        {
            var client = _container.GetBlobClient(data.FileName);
            return await client.ExistsAsync();            
        }
        public async Task<bool> Delete(BlobData data)
        {
            return await _container.DeleteBlobIfExistsAsync(data.FileName);            
        }
        public async Task<BlobData> DownloadH2KFile(string fileName)
        {
            var client = _container.GetBlobClient(fileName);
            var output = await client.DownloadAsync();           
            return _mapper.Map<BlobDownloadInfo, BlobData>(output);
        }
    }
}
