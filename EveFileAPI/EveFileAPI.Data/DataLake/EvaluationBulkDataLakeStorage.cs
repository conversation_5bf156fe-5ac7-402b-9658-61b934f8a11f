﻿using AutoMapper;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using EveFileAPI.Domain.Blob;
using Microsoft.Extensions.Azure;
using System.Threading.Tasks;

namespace EveFileAPI.Data.DataLake
{

    public class EvaluationBulkDataLakeStorage : IEvaluationBulkUploadDataLakeStorage
    {

        private readonly BlobContainerClient _container;
        private readonly IMapper _mapper;
       
        public EvaluationBulkDataLakeStorage(IAzureClientFactory<BlobServiceClient> clientFactory, IMapper mapper)
        {
            BlobServiceClient client = clientFactory.CreateClient("StorageAccount");
            _container = client.GetBlobContainerClient("bulkh2kfilelanding");
            _mapper = mapper;
        }
        public async Task<bool> Add(BlobData data)
        {
            await _container.UploadBlobAsync(data.FileName, data.DataAsStream);
            return true;
        }
       
    }
}
