﻿using EveFileAPI.Domain.Evaluation;
using EveFileAPI.Domain.Meta;
using System.Linq;

namespace EveFileAPI.Data.Repository.Files
{
    public class MiscRepository : RepositoryBase<VEvaluation>, IMiscRepository
    {

        public MiscRepository(RepositoryContext repositoryContext) : base(repositoryContext)
        {

        }
        public bool ChannelExists(string channelName)
        {
            IQueryable<VChannel> evaluationIQ = from s in RepositoryContext.VChannels
                                                             where s.ChannelName == channelName
                                                             select s;
            var result = evaluationIQ.ToList();
            return (result != null && result.Count > 0);
                
        }

    }
}