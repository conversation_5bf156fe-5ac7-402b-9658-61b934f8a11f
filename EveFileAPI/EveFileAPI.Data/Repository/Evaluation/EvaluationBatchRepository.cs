﻿using EveFileAPI.Domain.Evaluation;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;

namespace EveFileAPI.Data.Repository.Files
{
    public class EvaluationBatchRepository : RepositoryBase<VEvaluationBatch>, IEvaluationBatchRepository
    {
        public EvaluationBatchRepository(RepositoryContext repositoryContext) : base(repositoryContext)
        {
        }
        public async Task<VEvaluationBatch> GetEvaluationBatchByIdAsync(int batchId)
        {
            IQueryable<VEvaluationBatch> evaluationIQ = from s in RepositoryContext.VEvaluationBatch
                                                        where s.BatchId == batchId
                                                        select s;
            var result = await evaluationIQ.ToListAsync();
            if (result != null && result.Count > 0)
                return result[0];
            return null;
        }

    }
}