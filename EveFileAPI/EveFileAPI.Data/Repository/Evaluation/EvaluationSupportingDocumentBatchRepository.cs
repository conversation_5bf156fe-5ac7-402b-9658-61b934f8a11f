﻿using EveFileAPI.Domain.Evaluation;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EveFileAPI.Data.Repository.Files
{
    public class EvaluationSupportingDocumentBatchRepository : RepositoryBase<VEvaluationSupportingDocumentBatch>, IEvaluationSupportingDocumentBatchRepository
    {
        public EvaluationSupportingDocumentBatchRepository(RepositoryContext repositoryContext) : base(repositoryContext)
        {
        }
        public async Task<List<VEvaluationSupportingDocumentBatch>> GetSupportingDocumentsAsync(int batchId)
        {
            IQueryable<VEvaluationSupportingDocumentBatch> evaluationIQ = from s in RepositoryContext.VEvaluationSupportingDocumentBatches
                                                                          where s.EvaluationFileBatchId == batchId
                                                                          select s; ;
            return await evaluationIQ.ToListAsync();
        }

    }
}