﻿using EveFileAPI.Domain.Evaluation;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;

namespace EveFileAPI.Data.Repository.Files
{
    public class EvaluationFileBatchRepository : RepositoryBase<VEvaluationFileBatch>, IEvaluationFileBatchRepository
    {
        public EvaluationFileBatchRepository(RepositoryContext repositoryContext) : base(repositoryContext)
        {
        }
        public async Task<VEvaluationFileBatch> GetEvaluationByIdentifierAsync(int batchId,string fileNumber)
        {
            IQueryable<VEvaluationFileBatch> evaluationIQ = from s in RepositoryContext.VEvaluationFileBatches
                                                            where s.BatchId == batchId &&  s.EvaluationFileName.ToUpper().Substring(0, s.EvaluationFileName.Length-4) == fileNumber.ToUpper()
                                                            select s;
            var result = await evaluationIQ.ToListAsync();
            if (result != null && result.Count > 0)
                return result[0];
            return null;
          
        }

    }
}