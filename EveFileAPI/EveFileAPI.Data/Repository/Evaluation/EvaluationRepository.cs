﻿using EveFileAPI.Domain.Evaluation;
using EveFileAPI.Domain.Request;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EveFileAPI.Data.Repository.Files
{
    public class EvaluationRepository : RepositoryBase<VEvaluation>, IEvaluationRepository
    {

        public EvaluationRepository(RepositoryContext repositoryContext) : base(repositoryContext)
        {

        }
        public async Task<VEvaluation> GetEvaluationByIdentifierAsync(string fileId)
        {
            IQueryable<VEvaluation> evaluationIQ = from s in RepositoryContext.VEvaluations                                                 
                                                   where s.Filenumber == fileId
                                                   select new VEvaluation
                                                   {
                                                       Evaluationsid = s.Evaluationsid,
                                                       Filenumber = s.Filenumber,
                                                       Clientaddr = s.Clientaddr,
                                                       Clientcity = s.Clientcity,
                                                       Evaluationstatus = s.Evaluationstatus,
                                                       BatchId = s.BatchId,
                                                       Clientpcode = s.Clientpcode,
                                                       SourceApplication= s.SourceApplication
                                                   };
            var result = await evaluationIQ.ToListAsync();
            if (result != null && result.Count > 0)
                return result[0];
            return null;
        }
        public VEvaluation GetEvaluationByIdentifier(string fileId)
        {
            IQueryable<VEvaluation> evaluationIQ = from s in RepositoryContext.VEvaluations
                                                   where s.Filenumber == fileId
                                                   select new VEvaluation
                                                   {
                                                       Evaluationsid = s.Evaluationsid,
                                                       Filenumber = s.Filenumber,
                                                       Clientaddr = s.Clientaddr,
                                                       Clientcity = s.Clientcity,
                                                       Evaluationstatus = s.Evaluationstatus,
                                                       BatchId = s.BatchId,
                                                       Clientpcode = s.Clientpcode,
                                                       SourceApplication = s.SourceApplication
                                                   };
            var result =  evaluationIQ.ToList();
            if (result != null && result.Count > 0)
                return result[0];
            return null;
        }
        public VEvaluationDateRecord GetEvaluationExtensionByIdentifier(string fileId)
        {
            IQueryable<VEvaluationDateRecord> evaluationIQ = from s in RepositoryContext.VEvaluations
                                                   join d in RepositoryContext.VEvaluationSupplementaryDates
                                                   on new { s.UniqueEvaluationsId, SupplementaryDatesKey = "OriginalSubmissionDate" } equals new { d.UniqueEvaluationsId,d.SupplementaryDatesKey } into grouping
                                                   from p in grouping.DefaultIfEmpty()
                                                   where s.Filenumber == fileId                                                   
                                                   select new VEvaluationDateRecord(s.Filenumber,s.Evaluationstatus,s.UniqueEvaluationsId,p.Id);
            var result = evaluationIQ.ToList();
            if (result != null && result.Count > 0)
                return result[0];
            return null;
        }

        public async Task<IEnumerable<VEvaluation>> GetEvaluationBySearch(EvaluationFileRequestVM requestFile)
        {
                       
            IQueryable<VEvaluation> evaluationIQ = from s in RepositoryContext.VEvaluations
                                                   from p in RepositoryContext.VProvinces 
                                                   where s.Filenumber == requestFile.FileNumber && (s.Houseregion == p.ProvinceNameEn || s.Houseregion == p.ProvinceNameFr)
                                                   select new VEvaluation
                                                   {
                                                       Evaluationsid = s.Evaluationsid,
                                                       Filenumber = s.Filenumber,
                                                       Clientaddr = s.Clientaddr,
                                                       Clientcity = s.Clientcity,
                                                       Province = p.ProvinceCode,
                                                       Evaluationstatus = s.Evaluationstatus,
                                                       BatchId = s.BatchId,
                                                       Clientname = s.Clientname,
                                                       Clientpcode = s.Clientpcode,
                                                       SourceApplication= s.SourceApplication
                                                   };
            return await evaluationIQ.ToListAsync();
        }
    }
}