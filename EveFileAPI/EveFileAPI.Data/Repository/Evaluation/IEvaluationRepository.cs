﻿using EveFileAPI.Domain.Evaluation;
using EveFileAPI.Domain.Request;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EveFileAPI.Data.Repository.Files
{
    public interface IEvaluationRepository
    {
        Task<VEvaluation> GetEvaluationByIdentifierAsync(string fileId);
        VEvaluation GetEvaluationByIdentifier(string fileId);
        VEvaluationDateRecord GetEvaluationExtensionByIdentifier(string fileId);
        Task<IEnumerable<VEvaluation>> GetEvaluationBySearch(EvaluationFileRequestVM requestFile);
    }
}