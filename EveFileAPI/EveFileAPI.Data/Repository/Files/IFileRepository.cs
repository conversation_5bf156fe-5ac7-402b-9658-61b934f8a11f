﻿using EveFileAPI.Domain.FileInfo;

namespace EveFileAPI.Data.Repository.Files
{
    public interface IFileRepository
    {
        EvaluationFileBatch CreateAsync(EvaluationFile file);
        EvaluationFileBatch UpdateSupportingDoumentAsync(string fileNumber, int batchId, EvaluationFile file);
        EvaluationBatch CreateBatchAsync(EvaluationFile file);
        string AddEsnhEvaluation(int batchNumber, string fileNumber, string clientName, string clientPostalCode, string houseRegion);

    }
}