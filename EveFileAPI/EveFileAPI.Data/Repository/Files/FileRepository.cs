﻿using EveFileAPI.Domain.FileInfo;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Data;

namespace EveFileAPI.Data.Repository.Files
{
    public class FileRepository : IFileRepository
    {
        private readonly RepositoryContext _repositoryContext;
        public FileRepository(RepositoryContext repositoryContext)
        {
            _repositoryContext = repositoryContext;
        }

        public EvaluationFileBatch CreateAsync(EvaluationFile file)
        {
            var param = new SqlParameter[]
           {
                new SqlParameter() {ParameterName = "@BatchId",Value = -1, SqlDbType= SqlDbType.Int},
                new SqlParameter() {ParameterName = "@SubmissionChannel",Value = file.ChannelName, SqlDbType= SqlDbType.NVarChar},
                new SqlParameter() {ParameterName = "@EvaluationFileName",Value = file.Name, SqlDbType= SqlDbType.NVarChar},
                new SqlParameter() {ParameterName = "@ReceivedEvaluationFileName",Value = file.Name, SqlDbType= SqlDbType.NVarChar},
                new SqlParameter() {ParameterName = "@SubmissionType",Value = file.SubmissionType, SqlDbType= SqlDbType.NVarChar},
                new SqlParameter() {ParameterName = "@ReceivedDirectoryName",Value = "Received", SqlDbType= SqlDbType.NVarChar},
                new SqlParameter() {ParameterName = "@SupportingDocuments",Value = JsonConvert.SerializeObject(file.SupportingDocument), SqlDbType= SqlDbType.NVarChar},
                new SqlParameter() {ParameterName = "@HomeOwnerDocuments",Value = JsonConvert.SerializeObject(file.HomeOwnerDocument), SqlDbType= SqlDbType.NVarChar},
                //When we want to track email for each upload/update
                //new SqlParameter() {ParameterName = "@EmailAddress",Value = file.EmailAddress, SqlDbType= SqlDbType.NVarChar},
                new SqlParameter() {ParameterName = "@JSONOUTPUT", SqlDbType= SqlDbType.NVarChar,Size=-1 , Direction=ParameterDirection.Output}
           };
            _repositoryContext.Database.ExecuteSqlRaw("[EEAPI].[s_SetEvaluationFileBatchJSON] @BatchId,@SubmissionChannel, @EvaluationFileName, @ReceivedEvaluationFileName, @SubmissionType, @ReceivedDirectoryName,@SupportingDocuments,@HomeOwnerDocuments, @JSONOUTPUT OUTPUT", param);
            //When we want to track email for each upload/update
            //_repositoryContext.Database.ExecuteSqlRaw("[EEAPI].[s_SetEvaluationFileBatchJSON] @BatchId,@SubmissionChannel, @EvaluationFileName, @ReceivedEvaluationFileName, @SubmissionType, @ReceivedDirectoryName,@SupportingDocuments,@HomeOwnerDocuments, @EmailAddress, @JSONOUTPUT OUTPUT", param);
            //_repositoryContext.Database.ExecuteSqlRaw("[EEAPI].[s_SetEvaluationFileBatchJSON] @BatchId,@SubmissionChannel, @EvaluationFileName, @ReceivedEvaluationFileName, @SubmissionType, @ReceivedDirectoryName,@SupportingDocuments, @JSONOUTPUT OUTPUT", param);

            var result = JsonConvert.DeserializeObject<List<EvaluationFileBatch>>(param[8].Value as string);

            return result[0];
        }
        public EvaluationFileBatch UpdateSupportingDoumentAsync(string fileNumber, int batchId, EvaluationFile file)
        {
            var param = new SqlParameter[]
           {
                new SqlParameter() {ParameterName = "@FileNumber",Value = fileNumber, SqlDbType= SqlDbType.NVarChar},
                new SqlParameter() {ParameterName = "@BatchId",Value = batchId, SqlDbType= SqlDbType.Int},
                new SqlParameter() {ParameterName = "@SubmissionType",Value = file.SubmissionType, SqlDbType= SqlDbType.NVarChar},
                new SqlParameter() {ParameterName = "@SupportingDocuments",Value = JsonConvert.SerializeObject(file.SupportingDocument), SqlDbType= SqlDbType.NVarChar},
                new SqlParameter() {ParameterName = "@HomeOwnerDocuments",Value = JsonConvert.SerializeObject(file.HomeOwnerDocument), SqlDbType= SqlDbType.NVarChar},
                new SqlParameter() {ParameterName = "@JSONOUTPUT", SqlDbType= SqlDbType.NVarChar,Size=-1 , Direction=ParameterDirection.Output}
           };
            _repositoryContext.Database.ExecuteSqlRaw("[EEAPI].[s_SetEvaluationSupportingFilesJSON] @FileNumber, @BatchId, @SubmissionType,@SupportingDocuments, @HomeOwnerDocuments, @JSONOUTPUT OUTPUT", param);
            var result = JsonConvert.DeserializeObject<List<EvaluationFileBatch>>(param[5].Value as string);
            return result[0];
        }
        public EvaluationBatch CreateBatchAsync(EvaluationFile file)
        {
            var param = new SqlParameter[]
               {
                    new SqlParameter() {ParameterName = "@SubmissionChannel",Value = file.ChannelName, SqlDbType= SqlDbType.NVarChar},
                    new SqlParameter() {ParameterName = "@BulkFileName",Value = file.Name, SqlDbType= SqlDbType.NVarChar},
                    new SqlParameter() {ParameterName = "@SubmissionType",Value = file.SubmissionType, SqlDbType= SqlDbType.NVarChar},
                    new SqlParameter() {ParameterName = "@EmailAddress",Value = file.EmailAddress, SqlDbType= SqlDbType.NVarChar},
                    new SqlParameter() {ParameterName = "@JSONOUTPUT", SqlDbType= SqlDbType.NVarChar,Size=-1 , Direction=ParameterDirection.Output}
               };
            _repositoryContext.Database.ExecuteSqlRaw("[EEAPI].[s_SetBatchJSON] @SubmissionChannel, @BulkFileName, @SubmissionType,@EmailAddress, @JSONOUTPUT OUTPUT", param);
            var result = JsonConvert.DeserializeObject<List<EvaluationBatch>>(param[4].Value as string);
            return result[0];
        }
        public string AddEsnhEvaluation(int batchNumber, string fileNumber,string clientName, string clientPostalCode, string houseRegion)
        {

            var param = new SqlParameter[]
            {
                new SqlParameter() { ParameterName = "@BatchId", Value = batchNumber, SqlDbType = SqlDbType.Int },
                new SqlParameter() { ParameterName = "@FileNumber", Value = fileNumber, SqlDbType = SqlDbType.NVarChar },
                new SqlParameter() { ParameterName = "@clientName", Value = clientName, SqlDbType = SqlDbType.NVarChar },
                new SqlParameter() { ParameterName = "@clientPostalCode", Value = clientPostalCode, SqlDbType = SqlDbType.NVarChar },
                new SqlParameter() { ParameterName = "@houseRegion", Value = houseRegion, SqlDbType = SqlDbType.NVarChar },
                new SqlParameter() { ParameterName = "@output", SqlDbType = SqlDbType.Int, Direction = ParameterDirection.Output }
            };
            _repositoryContext.Database.ExecuteSqlRaw("[EEAPI].[s_SetEvaluationsEsnh] @BatchId, @FileNumber,@clientName,@clientPostalCode,@houseRegion, @output OUTPUT", param);

            var result = param[5].Value.ToString();
            return result;
        }
    }
}